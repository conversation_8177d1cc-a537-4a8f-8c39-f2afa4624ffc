import React from 'react'
import { cn } from '@/lib/utils'
import { Card, CardContent } from '@/components/ui/card'
import type { TaskInfo } from '@/types/task.types'
import { TaskHeader } from './task/TaskHeader'
import { LogContainer } from './task/LogContainer'
import { TaskFooter } from './task/TaskFooter'

export interface TaskMessageProps {
  task: TaskInfo
  isLive?: boolean
  onCancel?: (taskId: string) => void
  onRetry?: (command: string) => void
  className?: string
}

/**
 * TaskMessage component displays real-time task execution status
 * with command info, status indicators, logs, and interactive controls
 */
export const TaskMessage: React.FC<TaskMessageProps> = ({
  task,
  isLive = false,
  onCancel,
  onRetry,
  className
}) => {
  return (
    <Card className={cn(
      "task-message mb-4 transition-all duration-200 hover:shadow-md",
      className
    )}>
      <CardContent className="p-4">
        <TaskHeader
          task={task}
        />

        <LogContainer
          logs={task.logs || []}
          isLive={isLive}
        />

        <TaskFooter
          task={task}
          onRetry={onRetry}
          onCancel={onCancel}
        />
      </CardContent>
    </Card>
  )
}

export default TaskMessage
