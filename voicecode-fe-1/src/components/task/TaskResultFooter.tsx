import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON>lt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { RotateCcw } from 'lucide-react'
import { cn } from '@/lib/utils'
import type { TaskInfo } from '@/types/task.types'

interface TaskResultFooterProps {
  task: TaskInfo
  onRetry?: (command: string) => void
  className?: string
}

/**
 * TaskResultFooter provides retry functionality for failed tasks
 */
export const TaskResultFooter: React.FC<TaskResultFooterProps> = ({
  task,
  onRetry,
  className
}) => {
  const handleRetry = () => {
    if (onRetry) {
      onRetry(task.command)
    }
  }

  // Only show footer for failed tasks with retry callback
  const showRetry = task.status === 'failed' && onRetry
  
  if (!showRetry) {
    return null
  }

  return (
    <TooltipProvider>
      <div className={cn(
        "flex items-center justify-center pt-3 mt-3 border-t border-border",
        className
      )}>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRetry}
              className="h-8 px-3 text-xs"
            >
              <RotateCcw className="h-3 w-3 mr-1" />
              Retry
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Retry this command</p>
          </TooltipContent>
        </Tooltip>
      </div>
    </TooltipProvider>
  )
}

export default TaskResultFooter