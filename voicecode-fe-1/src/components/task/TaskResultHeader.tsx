import React from 'react'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { 
  Terminal, 
  CheckCircle,
  XCircle,
  Clock,
  Copy
} from 'lucide-react'
import { cn } from '@/lib/utils'
import type { TaskInfo } from '@/types/task.types'

interface TaskResultHeaderProps {
  task: TaskInfo
  summary?: string
  className?: string
}

/**
 * TaskResultHeader displays the command, status, and timing information
 */
export const TaskResultHeader: React.FC<TaskResultHeaderProps> = ({
  task,
  summary,
  className
}) => {
  const isSuccess = task.exit_code === 0
  const executionTime = task.execution_time

  const handleCopyCommand = () => {
    navigator.clipboard.writeText(task.command)
  }

  return (
    <div className={cn("space-y-3", className)}>
      {/* Command and Status Row */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
        <div className="flex items-center gap-2 min-w-0 flex-1">
          <Terminal className="h-4 w-4 flex-shrink-0" />
          <code className="text-sm font-mono truncate">$ {task.command}</code>
        </div>
        
        <div className="flex items-center gap-2 flex-shrink-0">
          <Badge variant={isSuccess ? "default" : "destructive"} className="text-xs">
            {isSuccess ? (
              <CheckCircle className="h-3 w-3 mr-1" />
            ) : (
              <XCircle className="h-3 w-3 mr-1" />
            )}
            Exit {task.exit_code}
          </Badge>
          
          {executionTime && (
            <Badge variant="outline" className="text-xs">
              <Clock className="h-3 w-3 mr-1" />
              {executionTime.toFixed(2)}s
            </Badge>
          )}
          
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={handleCopyCommand}
            className="h-7 px-2"
          >
            <Copy className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Task ID Badge */}
      <div className="flex items-center gap-2">
        <Badge variant="outline" className="text-xs">
          Task {task.id.slice(-8)}
        </Badge>
      </div>

      {/* Summary */}
      {summary && (
        <div className="text-sm text-muted-foreground bg-muted/30 p-2 rounded">
          {summary}
        </div>
      )}
    </div>
  )
}

export default TaskResultHeader