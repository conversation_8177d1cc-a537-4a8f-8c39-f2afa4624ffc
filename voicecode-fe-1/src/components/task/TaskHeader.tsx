import React from 'react'
import { Badge } from '@/components/ui/badge'
import { 
  Terminal, 
  Loader2,
  Play,
  CheckCircle,
  XCircle,
  StopCircle
} from 'lucide-react'
import { cn } from '@/lib/utils'
import type { TaskInfo } from '@/types/task.types'

interface TaskHeaderProps {
  task: TaskInfo
  className?: string
}

/**
 * TaskHeader displays the command being executed and provides action buttons
 */
export const TaskHeader: React.FC<TaskHeaderProps> = ({
  task,
  className
}) => {

  // Status configuration
  const getStatusConfig = (status: TaskInfo['status']) => {
    switch (status) {
      case 'pending':
        return {
          icon: Loader2,
          color: 'text-blue-500',
          text: 'Pending',
          variant: 'secondary' as const,
          animate: true
        }
      case 'running':
        return {
          icon: Play,
          color: 'text-green-500',
          text: 'Running',
          variant: 'default' as const,
          animate: false
        }
      case 'completed':
        return {
          icon: CheckCircle,
          color: 'text-green-600',
          text: 'Completed',
          variant: 'default' as const,
          animate: false
        }
      case 'failed':
        return {
          icon: XCircle,
          color: 'text-red-500',
          text: 'Failed',
          variant: 'destructive' as const,
          animate: false
        }
      case 'cancelled':
        return {
          icon: StopCircle,
          color: 'text-yellow-500',
          text: 'Cancelled',
          variant: 'secondary' as const,
          animate: false
        }
    }
  }

  const statusConfig = getStatusConfig(task.status)
  const StatusIcon = statusConfig.icon

  return (
    <div className={cn(
      "flex items-start justify-between gap-3 mb-3",
      className
    )}>
      {/* Command Display */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1 flex-wrap">
          <Terminal className="h-4 w-4 text-muted-foreground flex-shrink-0" />
          <Badge variant="outline" className="text-xs">
            Task {task.id.slice(-8)}
          </Badge>
          
          {/* Status Badge */}
          <Badge variant={statusConfig.variant} className="text-xs">
            <StatusIcon className={cn(
              "h-3 w-3 mr-1",
              statusConfig.color,
              statusConfig.animate && "animate-spin"
            )} />
            {statusConfig.text}
          </Badge>
        </div>

        <div className="bg-muted rounded-md p-2 font-mono text-sm break-all">
          <code className="text-foreground">{task.command}</code>
        </div>

        {task.working_directory && (
          <div className="text-xs text-muted-foreground mt-1">
            Working directory: <code>{task.working_directory}</code>
          </div>
        )}
      </div>
    </div>
  )
}

export default TaskHeader
