import React, { useEffect, useRef, useState } from 'react'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { ChevronDown } from 'lucide-react'
import { cn } from '@/lib/utils'
import type { LogEntry } from '@/types/task.types'

interface LogContainerProps {
  logs: LogEntry[]
  isLive?: boolean
  maxHeight?: string
  className?: string
}

/**
 * LogContainer displays scrollable log output with auto-scroll and formatting
 */
export const LogContainer: React.FC<LogContainerProps> = ({
  logs,
  isLive = false,
  maxHeight = "300px",
  className
}) => {
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const [autoScroll, setAutoScroll] = useState(true)
  const [isAtBottom, setIsAtBottom] = useState(true)
  const [isCollapsed, setIsCollapsed] = useState(false)

  // Auto-scroll to bottom when new logs arrive (if auto-scroll is enabled)
  useEffect(() => {
    if (autoScroll && isLive && scrollAreaRef.current) {
      const scrollElement = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]')
      if (scrollElement) {
        scrollElement.scrollTop = scrollElement.scrollHeight
      }
    }
  }, [logs, autoScroll, isLive])

  // Handle scroll events to detect if user scrolled away from bottom
  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const target = event.target as HTMLDivElement
    const isNearBottom = target.scrollHeight - target.scrollTop - target.clientHeight < 50
    setIsAtBottom(isNearBottom)
    
    // Disable auto-scroll if user scrolls up
    if (!isNearBottom && autoScroll) {
      setAutoScroll(false)
    }
  }

  // Scroll to bottom manually
  const scrollToBottom = () => {
    if (scrollAreaRef.current) {
      const scrollElement = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]')
      if (scrollElement) {
        scrollElement.scrollTop = scrollElement.scrollHeight
        setAutoScroll(true)
        setIsAtBottom(true)
      }
    }
  }



  const formatTimestamp = (timestamp: string): string => {
    try {
      return new Date(timestamp).toLocaleTimeString()
    } catch {
      return timestamp
    }
  }

  const getLogTypeColor = (type: LogEntry['type']): string => {
    switch (type) {
      case 'stdout':
        return 'text-foreground'
      case 'stderr':
        return 'text-red-500'
      case 'system':
        return 'text-blue-500'
      default:
        return 'text-muted-foreground'
    }
  }

  const getLogTypeBadgeVariant = (type: LogEntry['type']) => {
    switch (type) {
      case 'stderr':
        return 'destructive' as const
      case 'system':
        return 'secondary' as const
      default:
        return 'outline' as const
    }
  }

  if (logs.length === 0) {
    return (
      <div className={cn(
        "text-center py-4 text-muted-foreground text-sm",
        className
      )}>
        {isLive ? 'Waiting for output...' : 'No output'}
      </div>
    )
  }

  return (
    <Collapsible
      open={!isCollapsed}
      onOpenChange={(open) => setIsCollapsed(!open)}
      className={cn("relative", className)}
    >
      {/* Log Header */}
      <div className="flex items-center justify-between mb-2">
        <CollapsibleTrigger asChild>
          <Button variant="ghost" className="flex items-center gap-2 p-0 h-auto">
            <span className="text-sm font-medium">Output</span>
            <Badge variant="outline" className="text-xs">
              {logs.length} {logs.length === 1 ? 'line' : 'lines'}
            </Badge>
            {isLive && (
              <Badge variant="secondary" className="text-xs animate-pulse">
                Live
              </Badge>
            )}
            <ChevronDown className={cn(
              "h-4 w-4 transition-transform duration-200",
              isCollapsed && "rotate-180"
            )} />
          </Button>
        </CollapsibleTrigger>

        {/* <div className="flex items-center gap-1">
          {logs.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={exportLogs}
              className="h-7 px-2 text-xs"
              title="Export logs"
            >
              <Download className="h-3 w-3 mr-1" />
              Export
            </Button>
          )}
        </div> */}
      </div>

      {/* Log Content */}
      <CollapsibleContent className="space-y-2">
        <div className="relative">
          <ScrollArea
            ref={scrollAreaRef}
            className="border rounded-md bg-muted/30"
            style={{ height: maxHeight }}
            onScrollCapture={handleScroll}
          >
            <div className="p-3 space-y-1 font-mono text-sm">
              {logs.map((log, index) => (
                <div
                  key={log.id || index}
                  className="flex items-start gap-2 hover:bg-muted/50 rounded px-1 py-0.5 -mx-1"
                >
                  <span className="text-xs text-muted-foreground flex-shrink-0 w-16">
                    {formatTimestamp(log.timestamp)}
                  </span>

                  <Badge
                    variant={getLogTypeBadgeVariant(log.type)}
                    className="text-xs h-4 px-1 flex-shrink-0"
                  >
                    {log.type}
                  </Badge>

                  <span className={cn(
                    "flex-1 break-words whitespace-pre-wrap",
                    getLogTypeColor(log.type)
                  )}>
                    {log.content}
                  </span>
                </div>
              ))}
            </div>
          </ScrollArea>

          {/* Scroll to bottom button */}
          {!isAtBottom && isLive && (
            <Button
              variant="secondary"
              size="sm"
              onClick={scrollToBottom}
              className="absolute bottom-2 right-2 h-8 px-2 shadow-md"
            >
              <ChevronDown className="h-3 w-3 mr-1" />
              <span className="text-xs">Latest</span>
            </Button>
          )}
        </div>
      </CollapsibleContent>
    </Collapsible>
  )
}

export default LogContainer
