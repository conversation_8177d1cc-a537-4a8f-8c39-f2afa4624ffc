import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { RotateCcw, X } from 'lucide-react'
import { cn } from '@/lib/utils'
import type { TaskInfo } from '@/types/task.types'

interface TaskFooterProps {
  task: TaskInfo
  onRetry?: (command: string) => void
  onCancel?: (taskId: string) => void
  className?: string
}

/**
 * TaskFooter provides context-aware action buttons based on task status
 */
export const TaskFooter: React.FC<TaskFooterProps> = ({
  task,
  onRetry,
  onCancel,
  className
}) => {
  const handleRetry = () => {
    if (onRetry) {
      onRetry(task.command)
    }
  }

  const handleCancel = () => {
    if (onCancel) {
      onCancel(task.id)
    }
  }

  // Show footer for failed tasks (retry) or running/pending tasks (cancel)
  const showRetry = task.status === 'failed' && onRetry
  const showCancel = (task.status === 'running' || task.status === 'pending') && onCancel
  const showFooter = showRetry || showCancel
  
  if (!showFooter) {
    return null
  }

  return (
    <TooltipProvider>
      <div className={cn(
        "flex items-center justify-center pt-3 mt-3 border-t border-border",
        className
      )}>
        {/* Show retry button for failed tasks */}
        {showRetry && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRetry}
                className="h-8 px-3 text-xs"
              >
                <RotateCcw className="h-3 w-3 mr-1" />
                Retry
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Retry this command</p>
            </TooltipContent>
          </Tooltip>
        )}

        {/* Show cancel button for running/pending tasks */}
        {showCancel && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                onClick={handleCancel}
                className="h-8 px-3 text-xs text-destructive hover:text-destructive"
              >
                <X className="h-3 w-3 mr-1" />
                Cancel
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Cancel running task</p>
            </TooltipContent>
          </Tooltip>
        )}
      </div>
    </TooltipProvider>
  )
}

export default TaskFooter
