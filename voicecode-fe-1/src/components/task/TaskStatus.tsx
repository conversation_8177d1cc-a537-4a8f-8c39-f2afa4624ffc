import React, { useEffect, useState } from 'react'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Loader2,
  Play,
  CheckCircle,
  XCircle,
  StopCircle,
  Clock
} from 'lucide-react'
import { cn } from '@/lib/utils'
import type { TaskStatus as TaskStatusType } from '@/types/task.types'

interface TaskStatusProps {
  status: TaskStatusType
  executionTime?: number
  exitCode?: number
  startedAt?: string
  completedAt?: string
  className?: string
}

/**
 * TaskStatus displays visual status indicators and execution information
 */
export const TaskStatus: React.FC<TaskStatusProps> = ({
  status,
  executionTime,
  exitCode,
  startedAt,
  className
}) => {
  const [liveExecutionTime, setLiveExecutionTime] = useState<number>(0)

  // Live execution time counter for running tasks
  useEffect(() => {
    if (status === 'running' && startedAt) {
      const startTime = new Date(startedAt).getTime()
      
      const interval = setInterval(() => {
        const now = Date.now()
        const elapsed = Math.floor((now - startTime) / 1000)
        setLiveExecutionTime(elapsed)
      }, 1000)

      return () => clearInterval(interval)
    }
  }, [status, startedAt])

  const statusConfig = {
    pending: {
      icon: Loader2,
      color: 'text-blue-500',
      message: 'Preparing command...',
      variant: 'secondary' as const,
      alertVariant: 'default' as const,
      animate: true
    },
    running: {
      icon: Play,
      color: 'text-green-500',
      message: 'Executing...',
      variant: 'secondary' as const,
      alertVariant: 'default' as const,
      animate: false
    },
    completed: {
      icon: CheckCircle,
      color: 'text-green-600',
      message: 'Completed',
      variant: 'default' as const,
      alertVariant: 'default' as const,
      animate: false
    },
    failed: {
      icon: XCircle,
      color: 'text-red-500',
      message: 'Failed',
      variant: 'destructive' as const,
      alertVariant: 'destructive' as const,
      animate: false
    },
    cancelled: {
      icon: StopCircle,
      color: 'text-yellow-500',
      message: 'Cancelled',
      variant: 'secondary' as const,
      alertVariant: 'default' as const,
      animate: false
    }
  }

  const config = statusConfig[status]
  const Icon = config.icon
  
  // Calculate display time
  const displayTime = status === 'running' ? liveExecutionTime : executionTime
  
  const formatTime = (seconds?: number): string => {
    if (!seconds) return '0s'
    
    if (seconds < 60) {
      return `${seconds}s`
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60
      return `${minutes}m ${remainingSeconds}s`
    } else {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      return `${hours}h ${minutes}m`
    }
  }

  return (
    <Alert variant={config.alertVariant} className={cn("mb-3", className)}>
      <Icon
        className={cn(
          "h-4 w-4",
          config.color,
          config.animate && "animate-spin"
        )}
      />
      <AlertDescription className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="flex flex-col">
            <Badge variant={config.variant} className="w-fit">
              {config.message}
            </Badge>

            {exitCode !== undefined && (
              <span className="text-xs text-muted-foreground mt-1">
                Exit code: {exitCode}
              </span>
            )}
          </div>
        </div>

        {/* Execution Time */}
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Clock className="h-4 w-4" />
          <span className="font-mono">
            {formatTime(displayTime)}
          </span>
        </div>
      </AlertDescription>
    </Alert>
  )
}

export default TaskStatus
