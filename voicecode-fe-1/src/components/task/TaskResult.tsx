import React from 'react'
import { cn } from '@/lib/utils'
import { Card, CardContent } from '@/components/ui/card'
import type { TaskInfo } from '@/types/task.types'
import { TaskResultHeader } from './TaskResultHeader'
import { TaskResultOutput } from './TaskResultOutput'
import { TaskResultFooter } from './TaskResultFooter'

export interface TaskResultProps {
  task: TaskInfo
  output: string
  summary?: string
  artifacts?: string[]
  onRetry?: (command: string) => void
  className?: string
}

/**
 * TaskResult component displays the final result of a completed task
 * with command info, status indicators, output, and interactive controls
 */
export const TaskResult: React.FC<TaskResultProps> = ({
  task,
  output,
  summary,
  artifacts = [],
  onRetry,
  className
}) => {
  const isSuccess = task.exit_code === 0
  const hasOutput = output && output.trim().length > 0

  return (
    <Card className={cn(
      "task-result mb-4 transition-all duration-200 hover:shadow-md",
      className
    )}>
      <CardContent className="p-4">
        <TaskResultHeader
          task={task}
          summary={summary}
        />

        {hasOutput && (
          <TaskResultOutput
            output={output}
            isSuccess={isSuccess}
            artifacts={artifacts}
          />
        )}

        <TaskResultFooter
          task={task}
          onRetry={onRetry}
        />
      </CardContent>
    </Card>
  )
}

export default TaskResult