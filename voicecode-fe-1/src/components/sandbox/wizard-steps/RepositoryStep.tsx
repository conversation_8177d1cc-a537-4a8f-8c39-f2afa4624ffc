import { useEffect, useState } from 'react'
import { Input } from '@/components/ui/input'
import { Card } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Skeleton } from '@/components/ui/skeleton'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Search, Star, Lock } from 'lucide-react'
import { useRepositoryStore } from '@/store/repository.store'
import { formatDistanceToNow } from 'date-fns'

export function RepositoryStep() {
  const [searchQuery, setSearchQuery] = useState('')
  const {
    repositories,
    branches,
    selectedRepository,
    selectedBranch,
    isLoadingRepos,
    isLoadingBranches,
    loadRepositories,
    selectRepository,
    selectBranch
  } = useRepositoryStore()


  useEffect(() => {
    loadRepositories()
  }, [loadRepositories])

  const filteredRepos = repositories.filter(repo =>
    repo.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    repo.description?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="space-y-3 sm:space-y-4 h-full flex flex-col">
      <div>
        <label className="text-sm font-medium mb-2 block">Repository</label>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search repositories..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>
      </div>

      <div className="flex-1 min-h-0">
        <ScrollArea className="h-[200px] sm:h-[300px] border rounded-lg">
          {isLoadingRepos ? (
            <div className="p-3 sm:p-4 space-y-2">
              {Array.from({ length: 5 }).map((_, i) => (
                <Skeleton key={i} className="h-14 sm:h-16 w-full" />
              ))}
            </div>
          ) : filteredRepos.length === 0 ? (
            <div className="p-6 sm:p-8 text-center text-muted-foreground">
              {searchQuery ? 'No repositories found' : 'No repositories available'}
            </div>
          ) : (
            <div className="p-2">
              {filteredRepos.map((repo) => (
                <Card
                  key={repo.id}
                  className={`
                    p-3 mb-2 cursor-pointer transition-all duration-200 hover:shadow-sm
                    ${selectedRepository?.id === repo.id 
                      ? 'border-primary bg-primary/5 shadow-sm' 
                      : 'hover:bg-muted/50'
                    }
                  `}
                   onClick={() => {
                     selectRepository(repo)
                   }}                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium truncate text-sm sm:text-base">{repo.name}</h4>
                        {repo.private && <Lock className="h-3 w-3 text-muted-foreground flex-shrink-0" />}
                      </div>
                      {repo.description && (
                        <p className="text-xs sm:text-sm text-muted-foreground line-clamp-2 sm:truncate mb-2">
                          {repo.description}
                        </p>
                      )}
                      <div className="flex items-center gap-3 sm:gap-4 text-xs text-muted-foreground">
                        <span className="flex items-center gap-1">
                          <Star className="h-3 w-3" />
                          {repo.stargazers_count}
                        </span>
                        <span className="truncate">
                          Updated {formatDistanceToNow(new Date(repo.updated_at), { addSuffix: true })}
                        </span>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </ScrollArea>
      </div>

      {selectedRepository && (
        <div className="flex-shrink-0">
          <label className="text-sm font-medium mb-2 block">Branch</label>
          {isLoadingBranches ? (
            <Skeleton className="h-9 w-full" />
          ) : branches.length > 0 ? (
            <Select 
              value={selectedBranch || ''} 
              onValueChange={(branch) => {
                selectBranch(branch)
              }}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a branch" />
              </SelectTrigger>
              <SelectContent>
                {branches.map((branch) => (
                  <SelectItem key={branch.name} value={branch.name}>
                    {branch.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : (
            <p className="text-sm text-muted-foreground">No branches available</p>
          )}
        </div>
      )}
    </div>
  )
}