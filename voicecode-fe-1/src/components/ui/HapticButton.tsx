import React from 'react';
import { cn } from '@/lib/utils';
import { useHaptic, type HapticPattern } from '@/utils/haptics';

interface HapticButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  hapticPattern?: HapticPattern;
  className?: string;
  disabled?: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any; // Allow additional HTML button attributes
}

export function HapticButton({
  children,
  onClick,
  hapticPattern = 'light',
  className,
  disabled = false,
  ...props
}: HapticButtonProps) {
  const { trigger } = useHaptic();

  const handleClick = () => {
    if (!disabled) {
      trigger(hapticPattern);
      onClick?.();
    }
  };

  return (
    <button
      className={cn(
        'transition-transform duration-100 active:scale-95',
        disabled && 'opacity-50 cursor-not-allowed',
        className
      )}
      onClick={handleClick}
      disabled={disabled}
      {...props}
    >
      {children}
    </button>
  );
}