import React, { useEffect, useState } from 'react';
import { App } from '@capacitor/app';
import { Capacitor } from '@capacitor/core';
import type { URLOpenListenerEvent } from '@capacitor/app';

export const DeepLinkTest: React.FC = () => {
  const [lastDeepLink, setLastDeepLink] = useState<string>('None');
  const [isNative, setIsNative] = useState(false);

  useEffect(() => {
    setIsNative(Capacitor.isNativePlatform());

    if (Capacitor.isNativePlatform()) {

      
      const listener = App.addListener('appUrlOpen', (event: URLOpenListenerEvent) => {
        setLastDeepLink(event.url);
      });

      return () => {
        listener.then(handle => handle.remove()).catch(console.error);
      };
    }
  }, []);

  const testDeepLink = () => {
    if (isNative) {
      // On native, we can't programmatically open deep links
      alert('To test deep link:\n1. Open Safari\n2. Type: voicecode://oauth/callback?code=test123&state=teststate\n3. Tap Go');
    } else {
      // On web, simulate the deep link
      window.location.href = 'voicecode://oauth/callback?code=test123&state=teststate';
    }
  };

  if (!isNative) {
    return (
      <div className="p-4 bg-yellow-100 rounded-lg">
        <p className="text-sm text-yellow-800">Deep link testing is only available on native platforms</p>
      </div>
    );
  }

  return (
    <div className="p-4 bg-blue-50 rounded-lg">
      <h3 className="font-semibold mb-2">Deep Link Test</h3>
      <p className="text-sm mb-2">Last deep link received: <code className="bg-gray-200 px-1 rounded">{lastDeepLink}</code></p>
      <button
        onClick={testDeepLink}
        className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700"
      >
        Test Deep Link
      </button>
      <p className="text-xs text-gray-600 mt-2">
        This will show instructions to manually test the deep link on iOS
      </p>
    </div>
  );
};