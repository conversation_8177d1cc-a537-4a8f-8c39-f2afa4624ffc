import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Send, Loader2, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "lucide-react"
import { useState, useEffect, useRef } from "react"
import { useSpeechRecognition } from "@/hooks/useSpeechRecognition"
import { useSpeechPermission } from "@/hooks/useSpeechPermission"
import { SpeechPermissionPrompt } from "@/components/speech/SpeechPermissionPrompt"

interface ChatInputProps {
  value: string
  onChange: (value: string) => void
  onSend: () => void
  isSending?: boolean
  placeholder?: string
  disabled?: boolean
  enableVoice?: boolean
}

export function ChatInput({
  value,
  onChange,
  onSend,
  isSending = false,
  placeholder = "Type your command or message...",
  disabled = false,
  enableVoice = true
}: ChatInputProps) {
  const [showPermissionPrompt, setShowPermissionPrompt] = useState(false)
  const [isProcessingVoice, setIsProcessingVoice] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  
  // Speech recognition hooks
  const { isGranted: permissionGranted, ensurePermissions } = useSpeechPermission()
  const {
    isListening,
    isStopping,
    transcript,
    finalTranscript,
    error: speechError,
    isSupported,
    start: startListening,
    stop: stopListening,
    reset: resetSpeech
  } = useSpeechRecognition()

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      onSend()
    }
  }

  // Update text field with speech transcript
  useEffect(() => {
    if (transcript && isListening) {
      // Show live transcript while listening
      onChange(transcript)
    }
  }, [transcript, isListening, onChange])

  // Handle final transcript and auto-send for voice commands
  useEffect(() => {
    if (finalTranscript && !isListening) {
      onChange(finalTranscript)
      resetSpeech()
      setIsProcessingVoice(true)
      
      // Auto-send voice commands after a short delay to allow user to see the text
      const timeoutId = setTimeout(() => {
        if (finalTranscript.trim()) {
          onSend()
        }
        setIsProcessingVoice(false)
      }, 800)
      
      return () => {
        clearTimeout(timeoutId)
        setIsProcessingVoice(false)
      }
    }
  }, [finalTranscript, isListening, onChange, resetSpeech, onSend])

  // Auto-resize textarea based on content
  useEffect(() => {
    const textarea = textareaRef.current
    if (textarea) {
      // Reset height to auto to get the correct scrollHeight
      textarea.style.height = 'auto'
      // Set height to scrollHeight
      textarea.style.height = `${textarea.scrollHeight}px`
    }
  }, [value])

  const handleVoiceToggle = async () => {
    if (!enableVoice || !isSupported) {
      return
    }

    if (isListening) {
      // Stop listening
      await stopListening()
    } else {
      try {
        // Check permissions first
        if (!permissionGranted) {
          const hasPermission = await ensurePermissions()
          if (!hasPermission) {
            setShowPermissionPrompt(true)
            return
          }
        }
        
        // Clear current text and start listening
        onChange("")
        resetSpeech()
        await startListening()
      } catch (error) {
        console.error('Error in voice toggle:', error)
      }
    }
  }

  const currentPlaceholder = isStopping
    ? "Finalizing transcript..."
    : isListening 
    ? "Listening... Speak now" 
    : isProcessingVoice 
    ? "Processing voice command..." 
    : (transcript ? "Processing speech..." : placeholder)

  return (
    <>
      <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border border-border/20 rounded-2xl shadow-lg p-4 fixed bottom-6 left-4 right-4 z-50">
        <div className={`flex items-center gap-3 transition-all duration-300 ${
          isListening ? 'bg-blue-50/50 dark:bg-blue-900/20 rounded-lg p-2 -m-2' : ''
        }`}>
          <div className="flex-1 relative max-h-[40vh] overflow-y-auto">
            <Textarea
              ref={textareaRef}
              placeholder={currentPlaceholder}
              value={value}
              onChange={(e) => onChange(e.target.value)}
              onKeyDown={handleKeyDown}
              className={`min-h-[24px] resize-none border-0 bg-transparent p-0 placeholder:text-muted-foreground focus-visible:ring-0 focus-visible:ring-offset-0 transition-all duration-200 ${
                isListening ? 'placeholder:text-blue-600 placeholder:font-medium' : ''
              } ${
                isProcessingVoice ? 'placeholder:text-green-600 placeholder:font-medium' : ''
              }`}
              disabled={disabled || isSending || isListening || isProcessingVoice}
            />
            
            {/* Voice recording indicator */}
            {(isListening || isStopping) && (
              <div className="absolute right-2 top-1/2 -translate-y-1/2">
                <div className="flex items-center gap-2">
                  {isStopping ? (
                    <>
                      <Loader2 className="h-3 w-3 animate-spin text-orange-600" />
                      <span className="text-xs text-orange-600 font-medium">Finalizing</span>
                    </>
                  ) : (
                    <>
                      {/* Audio visualization bars */}
                      <div className="flex items-center gap-0.5">
                        {[...Array(4)].map((_, i) => (
                          <div
                            key={i}
                            className="w-1 bg-red-500 rounded-full animate-pulse"
                            style={{
                              height: `${Math.random() * 8 + 4}px`,
                              animationDelay: `${i * 100}ms`,
                              animationDuration: '600ms'
                            }}
                          />
                        ))}
                      </div>
                      <span className="text-xs text-red-600 font-medium">Recording</span>
                    </>
                  )}
                </div>
              </div>
            )}
            
            {/* Voice processing indicator */}
            {isProcessingVoice && (
              <div className="absolute right-2 top-1/2 -translate-y-1/2">
                <div className="flex items-center gap-2">
                  <Loader2 className="h-3 w-3 animate-spin text-green-600" />
                  <span className="text-xs text-green-600 font-medium">Processing</span>
                </div>
              </div>
            )}
          </div>

          {/* Voice button */}
          {enableVoice && isSupported && (
            <Button 
              onClick={handleVoiceToggle}
              disabled={disabled || isSending || isProcessingVoice || isStopping}
              size="icon"
              variant={isListening || isStopping ? "default" : "ghost"}
              className={`h-12 w-12 rounded-full transition-all duration-200 self-center ${
                isStopping
                  ? 'bg-orange-500 hover:bg-orange-600 shadow-md shadow-orange-200'
                  : isListening 
                  ? 'bg-red-500 hover:bg-red-600 animate-pulse shadow-md shadow-red-200' 
                  : 'hover:bg-blue-50 dark:hover:bg-blue-900/20'
              }`}
            >
              {isStopping ? (
                <Loader2 className="h-5 w-5 text-white animate-spin" />
              ) : isListening ? (
                <MicOff className="h-5 w-5 text-white" />
              ) : (
                <Mic className="h-5 w-5" />
              )}
            </Button>
          )}

          <Button 
            onClick={onSend} 
            disabled={!value.trim() || isSending || disabled || isListening || isProcessingVoice} 
            size="sm"
            className="h-8 w-8 p-0 rounded-full"
          >
            {isSending || isProcessingVoice ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </div>

        {/* Speech error display */}
        {speechError && (
          <div className="mt-2 text-xs text-red-500 bg-red-50 dark:bg-red-900/20 px-2 py-1 rounded">
            Voice Error: {speechError}
          </div>
        )}
      </div>

      {/* Permission prompt modal */}
      {showPermissionPrompt && (
        <SpeechPermissionPrompt 
          onClose={() => setShowPermissionPrompt(false)}
          onPermissionGranted={() => {
            setShowPermissionPrompt(false)
            // Try to start voice recognition after permission is granted
            handleVoiceToggle()
          }}
          onPermissionDenied={() => {
            setShowPermissionPrompt(false)
          }}
        />
      )}
    </>
  )
}