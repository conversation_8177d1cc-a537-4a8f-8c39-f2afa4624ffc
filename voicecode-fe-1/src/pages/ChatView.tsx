import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom'
import { useEffect, useRef, useState } from 'react'
import { Keyboard } from '@capacitor/keyboard'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { ArrowLeft, Loader2, Terminal, AlertCircle, RefreshCw, Trash2, MoreVertical, Play, Square } from 'lucide-react'
import { useAutoAnimate } from '@formkit/auto-animate/react'
import { useShallow } from 'zustand/react/shallow'
import { useSandboxStore } from '@/store/sandbox.store'
import { TypingIndicator } from '@/components/messages/TypingIndicator'
import { SandboxService } from '@/services/sandbox.service'
import { cn } from '@/lib/utils'
import { useVoiceCodeChat } from '@/hooks/useVoiceCodeChat'
import { ChatInput } from '@/components/chat/ChatInput'
import { TaskMessage } from '@/components/TaskMessage'
import { TaskResult } from '@/components/task/TaskResult'
import { FastAPIAdapter } from '@/lib/fastapi-adapter'
import type { TaskInfo, LogEntry } from '@/types/task.types'

export function ChatView() {
  const { sandboxId } = useParams()
  const navigate = useNavigate()
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const [messagesParent] = useAutoAnimate()
  
  // Use AI SDK chat hook instead of Zustand store
  const {
    messages,
    input: inputValue,
    setInput: setInputValue,
    handleSubmit,
    isLoadingHistory,
    isSendingMessage,
    error,
    reload: loadHistory,
    clearHistory,
    // Task-related properties
    activeTask,
    getTaskById,
    cancelTask,
    executeCommand,
    // Streaming properties
    isStreaming,
    streamingState
  } = useVoiceCodeChat(sandboxId!, {
    enableStreaming: true, // Explicitly enable streaming
    onCommandExecute: () => {
    },
    onError: (error) => {
      console.error('Chat error:', error)
    },
    onTaskStart: () => {
    },
    onTaskComplete: () => {
    },
    onTaskError: (error) => {
      console.error('Task error:', error)
    }
  })
  
  const { 
    sandbox,
    deleteSandbox,
    startSandbox,
    stopSandbox,
    loadSandbox
  } = useSandboxStore(useShallow((state) => ({
    sandbox: state.getSandbox(sandboxId!),
    deleteSandbox: state.deleteSandbox,
    startSandbox: state.startSandbox,
    stopSandbox: state.stopSandbox,
    loadSandbox: state.loadSandbox
  })))

  // Local loading states
  const [isStarting, setIsStarting] = useState(false)
  const [isStopping, setIsStopping] = useState(false)



  // Helper function to convert message to TaskInfo with logs
  const getTaskInfoFromMessages = (taskId: string): TaskInfo | null => {
    const taskInfo = getTaskById(taskId)
    if (!taskInfo) return null

    // Collect all log messages for this task
    const taskMessages = messages.filter(msg => {
      const metadata = (msg as unknown as { metadata?: Record<string, unknown> }).metadata
      return metadata?.task_id === taskId && metadata?.type === 'task_log'
    })

    const logs: LogEntry[] = taskMessages.map((msg, index) => {
      const metadata = (msg as unknown as { metadata?: Record<string, unknown> }).metadata
      return {
        id: msg.id || `log-${index}`,
        content: msg.content,
        timestamp: metadata?.timestamp as string || new Date().toISOString(),
        type: (metadata?.log_type as LogEntry['type']) || 'stdout',
        original_timestamp: metadata?.original_timestamp as string
      }
    })

    return {
      ...taskInfo,
      logs
    }
  }

  // Helper function to handle task actions
  const handleTaskCancel = () => {
    cancelTask()
  }

  const handleTaskRetry = (command: string) => {
    executeCommand(command)
  }



  

  // Auto scroll to bottom on new messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Keyboard event listeners for iOS
  useEffect(() => {
    const handleKeyboardShow = (info: { keyboardHeight: number }) => {
      document.body.classList.add('keyboard-open')
      document.documentElement.style.setProperty('--keyboard-height', `${info.keyboardHeight}px`)
    }

    const handleKeyboardHide = () => {
      document.body.classList.remove('keyboard-open')
      document.documentElement.style.setProperty('--keyboard-height', '0px')
    }

    // Add keyboard event listeners
    Keyboard.addListener('keyboardWillShow', handleKeyboardShow)
    Keyboard.addListener('keyboardWillHide', handleKeyboardHide)

    // Cleanup listeners on unmount
    return () => {
      Keyboard.removeAllListeners()
    }
  }, [])



  const handleSend = async () => {
    if (!inputValue.trim() || isSendingMessage) return
    
    handleSubmit()
  }



  const handleDeleteSandbox = async () => {
    if (!sandboxId) return
    if (!confirm('Are you sure you want to delete this sandbox?')) return
    
    try {
      await deleteSandbox(sandboxId)
      navigate('/conversations')
    } catch (error) {
      console.error('Failed to delete sandbox:', error)
    }
  }

  const handleStartSandbox = async () => {
    if (!sandboxId || isStarting) return
    
    try {
      setIsStarting(true)
      await startSandbox(sandboxId)
      
      // Refresh sandbox status after operation completes
      await loadSandbox(sandboxId)
    } catch (error) {
      console.error('❌ Failed to start sandbox:', error)
    } finally {
      setIsStarting(false)
    }
  }

  const handleStopSandbox = async () => {
    if (!sandboxId || isStopping) return
    
    try {
      setIsStopping(true)
      await stopSandbox(sandboxId)
      
      // Refresh sandbox status after operation completes
      await loadSandbox(sandboxId)
    } catch (error) {
      console.error('❌ Failed to stop sandbox:', error)
    } finally {
      setIsStopping(false)
    }
  }



  if (!sandbox) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">Sandbox not found</p>
          <Button 
            onClick={() => navigate('/conversations')} 
            className="mt-4"
            variant="outline"
          >
            Back to Sandboxes
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="mobile-layout">
      {/* Fixed Header */}
      <div className="mobile-header">
        <div className="px-4 py-3 flex items-center justify-between">
          <div className="flex items-center gap-3 min-w-0 flex-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate('/conversations')}
              className="flex-shrink-0"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <Avatar className="flex-shrink-0 h-8 w-8">
              <AvatarImage src={`/placeholder.svg?height=32&width=32&text=${sandbox.sandbox_name.slice(0, 2).toUpperCase()}`} />
              <AvatarFallback className="text-sm">{sandbox.sandbox_name.slice(0, 2).toUpperCase()}</AvatarFallback>
            </Avatar>
            <div className="flex items-center gap-2 min-w-0 flex-1">
              <Badge variant={sandbox.status === "running" ? "default" : "secondary"} className="text-xs">
                <Terminal className="h-3 w-3 mr-1" />
                {SandboxService.getStatusText(sandbox.status)}
              </Badge>
              
              {/* Streaming Status Indicator - only show when actively streaming */}
              {isStreaming && activeTask && (activeTask.status === 'running' || activeTask.status === 'pending') && (
                <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                  <div className="h-2 w-2 bg-blue-500 rounded-full animate-pulse mr-1" />
                  Streaming
                </Badge>
              )}
              
              {streamingState.connectionState === 'connecting' && isStreaming && (
                <Badge variant="outline" className="text-xs bg-yellow-50 text-yellow-700 border-yellow-200">
                  <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                  Connecting
                </Badge>
              )}
              
              {streamingState.connectionState === 'error' && streamingState.lastError && (
                <Badge variant="outline" className="text-xs bg-red-50 text-red-700 border-red-200">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  Stream Error
                </Badge>
              )}
              
              {/* Sandbox Controls */}
              {(sandbox.status === 'stopped' || sandbox.status === 'error') && !isStopping ? (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleStartSandbox}
                  disabled={isStarting}
                  className="h-6 px-2 text-xs"
                >
                  {isStarting ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : (
                    <Play className="h-3 w-3" />
                  )}
                </Button>
              ) : sandbox.status === 'running' && !isStarting ? (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleStopSandbox}
                  disabled={isStopping}
                  className="h-6 px-2 text-xs"
                >
                  {isStopping ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : (
                    <Square className="h-3 w-3" />
                  )}
                </Button>
              ) : null}
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="flex-shrink-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => loadHistory()}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh Chat
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => clearHistory()}>
                <Trash2 className="h-4 w-4 mr-2" />
                Clear History
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={handleDeleteSandbox}
                className="text-destructive"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Sandbox
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Messages Content */}
      <div className="mobile-content">
        <div className="p-4 space-y-4 pb-28" ref={messagesParent}>
        {isLoadingHistory ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
          </div>
        ) : messages.length === 0 ? (
          <div className="text-center py-16">
            <Terminal className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400 mb-2">
              No messages yet
            </p>
            <p className="text-sm text-gray-500">
              Type a command to get started
            </p>
          </div>
        ) : (
          messages.map((message) => {
            const metadata = (message as unknown as { metadata?: Record<string, unknown> }).metadata || {}
            const messageType = message.role === 'user' ? 'user' : (metadata.messageType || 'system')
            const isOwnMessage = messageType === 'user'

            // Check if this is a task start message - render TaskMessage component
            if (metadata.type === 'task_start' && metadata.task_id) {
              const taskInfo = getTaskInfoFromMessages(metadata.task_id as string)
              if (taskInfo) {
                const isLive = activeTask?.id === metadata.task_id
                return (
                  <TaskMessage
                    key={`task-${metadata.task_id}`}
                    task={taskInfo}
                    isLive={isLive}
                    onCancel={handleTaskCancel}
                    onRetry={handleTaskRetry}
                  />
                )
              }
            }

            // Skip task log messages as they're included in TaskMessage
            if (metadata.type === 'task_log') {
              return null
            }

            if (messageType === 'system' && metadata.exitCode !== undefined) {
              // Create TaskInfo from message metadata
              const taskInfo = FastAPIAdapter.createTaskInfoFromMetadata(
                message.id,
                sandboxId!,
                metadata
              )

              return (
                <TaskResult
                  key={message.id}
                  task={taskInfo}
                  output={message.content}
                  summary={metadata.summary as string}
                  artifacts={metadata.artifacts as string[]}
                  onRetry={handleTaskRetry}
                />
              )
            }

            if (messageType === 'error') {
              return (
                <Alert key={message.id} variant="destructive">
                  <AlertDescription>{message.content}</AlertDescription>
                </Alert>
              )
            }

            if (messageType === 'system') {
              return (
                <Alert key={message.id}>
                  <AlertDescription>{message.content}</AlertDescription>
                </Alert>
              )
            }

            return (
              <div key={message.id} className={isOwnMessage ? "flex justify-end" : "flex justify-start"}>
                <div className={cn(
                  "rounded-lg px-3 py-2 max-w-[85%] sm:max-w-[80%] break-words",
                  isOwnMessage 
                    ? "bg-primary text-primary-foreground" 
                    : "bg-muted"
                )}>
                  <div className="whitespace-pre-wrap">{message.content}</div>
                </div>
              </div>
            )
          })
        )}
        
        {/* Temporarily always show TypingIndicator for styling */}
        {/* <TypingIndicator /> */}
        
        {isSendingMessage && (
          <TypingIndicator />
        )}
        
        <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Fixed Footer - Input Area */}
      <div className="mobile-footer">
        <div className="px-4 pb-6">
          {/* Error display */}
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Chat Input */}
          <ChatInput
            value={inputValue}
            onChange={setInputValue}
            onSend={handleSend}
            isSending={isSendingMessage}
          />
        </div>
      </div>
    </div>
  )
}