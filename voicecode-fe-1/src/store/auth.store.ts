import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { Capacitor } from '@capacitor/core'
import { AuthService } from '@/services/auth.service'
import { SecureTokenManager } from '@/services/secure-token-manager'
import type { AuthUser } from '@/types/auth.types'

interface AuthState {
  user: AuthUser | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

interface AuthActions {
  login: () => Promise<void>
  handleCallback: (authKey: string) => Promise<void>
  logout: () => void
  checkAuth: () => Promise<void>
  setError: (error: string | null) => void
  clearError: () => void
  clearStorage: () => void
}

export const useAuthStore = create<AuthState & AuthActions>()(
  devtools(
    persist(
      (set, get) => ({
        // State
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,

        // Actions
        login: async () => {
          set({ isLoading: true, error: null });
          
          try {
            if (Capacitor.isNativePlatform()) {
              
              // Direct mobile authentication with PKCE
              const authData = await AuthService.authenticateMobile();
              
              set({
                user: authData.user,
                token: authData.access_token,
                isAuthenticated: true,
                isLoading: false,
                error: null
              });

            } else {
              
              // Existing web flow
              const response = await AuthService.initiateGitHubAuth();
              if (response?.auth_url) {
                window.location.href = response.auth_url;
              }
            }
          } catch (error) {
            console.error('🔑❌ Auth Store: Login failed:', error);
            set({ 
              error: error instanceof Error ? error.message : 'Login failed',
              isLoading: false 
            });
          }
        },

        handleCallback: async (authKey: string) => {
          set({ isLoading: true, error: null })
          try {
            const authData = await AuthService.handleGitHubCallback(authKey)
            set({
              user: authData.user,
              token: authData.access_token,
              isAuthenticated: true,
              isLoading: false,
              error: null
            })
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Authentication failed',
              isLoading: false
            })
            throw error
          }
        },

        logout: async () => {
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            error: null
          })
          
          // Clear secure storage on mobile
          if (Capacitor.isNativePlatform()) {
            await SecureTokenManager.clearStoredTokens();
          }
          
          AuthService.logout()
        },

        checkAuth: async () => {
          const state = get()
          
          // On mobile, check secure storage first
          if (Capacitor.isNativePlatform() && !state.token) {
            const { token, user } = await SecureTokenManager.getStoredTokens();
            if (token && user) {
              set({ token, user, isAuthenticated: true })
              return
            }
          }
          
          if (state.token && !state.user) {
            try {
              const user = await AuthService.getCurrentUser()
              set({ user, isAuthenticated: true })
            } catch (error) {
              get().logout()
            }
          } else if (!state.token) {
            set({ isAuthenticated: false })
          } else {
            console.log('🔐 Auth Store: User already authenticated')
          }
        },

        setError: (error) => set({ error }),
        
        clearError: () => set({ error: null }),

        clearStorage: () => {
          localStorage.removeItem('voicecode-auth-store')
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            error: null
          })
        },
      }),
      {
        name: 'voicecode-auth-store',
        partialize: (state) => ({ 
          user: state.user, 
          token: state.token
        }),
        onRehydrateStorage: () => (state) => {
          if (state) {
            // Only set isAuthenticated to true if we have both user and token
            state.isAuthenticated = !!(state.user && state.token)
          }
        }
      }
    ),
    { name: 'auth-store' }
  )
)