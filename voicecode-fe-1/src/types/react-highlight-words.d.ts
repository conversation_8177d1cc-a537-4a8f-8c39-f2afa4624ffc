/* eslint-disable @typescript-eslint/no-explicit-any */
// Type definitions for external react-highlight-words library
declare module 'react-highlight-words' {
  import { ComponentType } from 'react';

  interface HighlighterProps {
    highlightClassName?: string;
    highlightStyle?: React.CSSProperties;
    highlightTag?: keyof JSX.IntrinsicElements | ComponentType<any>;
    searchWords: string[];
    textToHighlight: string;
    autoEscape?: boolean;
    caseSensitive?: boolean;
    findChunks?: (options: any) => any[];
    sanitize?: (text: string) => string;
    unhighlightClassName?: string;
    unhighlightStyle?: React.CSSProperties;
    unhighlightTag?: keyof JSX.IntrinsicElements | ComponentType<any>;
  }

  const Highlighter: ComponentType<HighlighterProps>;
  export default Highlighter;
}