/**
 * Streaming utilities for SSE connection management and message processing
 */

import type { SSEMessage, TaskError, StreamingConnectionState } from '@/types/task.types'

export interface StreamingOptions {
  enableStreaming?: boolean
  maxReconnectAttempts?: number
  reconnectDelay?: number
  connectionTimeout?: number
}

export const DEFAULT_STREAMING_OPTIONS: Required<StreamingOptions> = {
  enableStreaming: true,
  maxReconnectAttempts: 3,
  reconnectDelay: 1000,
  connectionTimeout: 30000
}

export interface ConnectionManager {
  connect: () => Promise<void>
  disconnect: () => void
  reconnect: () => Promise<void>
  getConnectionState: () => StreamingConnectionState
  getReconnectAttempts: () => number
  isConnected: () => boolean
}

/**
 * Parse SSE message from event data
 */
export function parseSSEMessage(data: string): SSEMessage | null {
  try {
    if (data === '[DONE]') {
      return null // Stream termination signal
    }
    
    const parsed = JSON.parse(data)
    
    // Validate required fields
    if (!parsed.id || !parsed.role || !parsed.content) {
      console.warn('Invalid SSE message format:', parsed)
      return null
    }
    
    return parsed as SSEMessage
  } catch (error) {
    console.error('Failed to parse SSE message:', error, 'Data:', data)
    return null
  }
}

/**
 * Create EventSource with proper error handling and reconnection
 */
export function createEventSource(
  url: string,
  options: {
    onMessage: (message: SSEMessage) => void
    onError: (error: TaskError) => void
    onConnectionChange: (state: StreamingConnectionState) => void
    signal?: AbortSignal
  }
): EventSource {
  const eventSource = new EventSource(url)
  
  let connectionTimeout: NodeJS.Timeout | null = null
  
  // Set connection timeout
  connectionTimeout = setTimeout(() => {
    options.onError({
      code: 'CONNECTION_TIMEOUT',
      message: 'Connection timeout',
      recoverable: true
    })
    eventSource.close()
  }, DEFAULT_STREAMING_OPTIONS.connectionTimeout)
  
  eventSource.onopen = () => {
    if (connectionTimeout) {
      clearTimeout(connectionTimeout)
      connectionTimeout = null
    }
    options.onConnectionChange('connected')
  }
  
  eventSource.onmessage = (event) => {
    const message = parseSSEMessage(event.data)
    if (message) {
      options.onMessage(message)
    } else if (event.data === '[DONE]') {
      // Stream completed normally
      eventSource.close()
      options.onConnectionChange('disconnected')
    }
  }
  
  eventSource.onerror = () => {
    if (connectionTimeout) {
      clearTimeout(connectionTimeout)
      connectionTimeout = null
    }
    
    const error: TaskError = {
      code: 'CONNECTION_ERROR',
      message: 'Streaming connection error',
      recoverable: eventSource.readyState !== EventSource.CLOSED
    }
    
    options.onError(error)
    
    if (eventSource.readyState === EventSource.CLOSED) {
      options.onConnectionChange('disconnected')
    } else {
      options.onConnectionChange('error')
    }
  }
  
  // Handle abort signal
  if (options.signal) {
    options.signal.addEventListener('abort', () => {
      if (connectionTimeout) {
        clearTimeout(connectionTimeout)
      }
      eventSource.close()
      options.onConnectionChange('disconnected')
    })
  }
  
  return eventSource
}

/**
 * Build streaming URL with authentication
 */
export function buildStreamingUrl(sandboxId: string, baseUrl?: string): string {
  const base = baseUrl || import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'
  const streamingUrl = `${base}/api/sandbox/${sandboxId}/tasks/stream`
  return streamingUrl
}

/**
 * Debounce function for rapid message updates
 */
export function debounce<T extends (...args: unknown[]) => void>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }
}

/**
 * Create task error from various error sources
 */
export function createTaskError(
  error: unknown,
  taskId?: string,
  recoverable = true
): TaskError {
  if (error instanceof Error) {
    return {
      code: 'TASK_ERROR',
      message: error.message,
      task_id: taskId,
      recoverable,
      timestamp: new Date().toISOString()
    }
  }
  
  if (typeof error === 'string') {
    return {
      code: 'TASK_ERROR',
      message: error,
      task_id: taskId,
      recoverable,
      timestamp: new Date().toISOString()
    }
  }
  
  return {
    code: 'UNKNOWN_ERROR',
    message: 'An unknown error occurred',
    task_id: taskId,
    recoverable,
    timestamp: new Date().toISOString()
  }
}

/**
 * Validate streaming support
 */
export function isStreamingSupported(): boolean {
  return typeof EventSource !== 'undefined'
}

/**
 * Get connection state display text
 */
export function getConnectionStateText(state: StreamingConnectionState): string {
  switch (state) {
    case 'connecting':
      return 'Connecting...'
    case 'connected':
      return 'Connected'
    case 'disconnected':
      return 'Disconnected'
    case 'error':
      return 'Connection Error'
    case 'reconnecting':
      return 'Reconnecting...'
    default:
      return 'Unknown'
  }
}

/**
 * Streaming Connection Manager
 * Handles connection lifecycle, reconnection logic, and connection state
 */
export class StreamingConnectionManager implements ConnectionManager {
  private connectionState: StreamingConnectionState = 'disconnected'
  private reconnectAttempts = 0
  private reconnectTimer: NodeJS.Timeout | null = null
  private connectionPromise: Promise<void> | null = null
  private url: string
  private command: string
  private authToken: string | null
  private options: Required<StreamingOptions>
  private callbacks: {
    onMessage: (message: SSEMessage) => void
    onError: (error: TaskError) => void
    onConnectionChange: (state: StreamingConnectionState) => void
  }

  constructor(
    url: string,
    command: string,
    options: Required<StreamingOptions>,
    callbacks: {
      onMessage: (message: SSEMessage) => void
      onError: (error: TaskError) => void
      onConnectionChange: (state: StreamingConnectionState) => void
    },
    authToken?: string
  ) {
    this.url = url
    this.command = command
    this.authToken = authToken || null
    this.options = options
    this.callbacks = callbacks
  }

  async connect(): Promise<void> {
    if (this.connectionPromise) {
      return this.connectionPromise
    }

    this.connectionPromise = this._connect()
    return this.connectionPromise
  }

  private async _connect(): Promise<void> {
    this.setConnectionState('connecting')

    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream'
      }
      
      // Add authorization header if token is available
      if (this.authToken) {
        headers['Authorization'] = `Bearer ${this.authToken}`
      }
      
      const response = await fetch(this.url, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          command: this.command
        })
      })

      if (!response.ok) {
        await response.text().catch(() => 'No error details')
        throw new Error(`Connection failed: ${response.status} ${response.statusText}`)
      }

      this.setConnectionState('connected')
      this.reconnectAttempts = 0

      // Process the stream
      await this.processStream(response)

    } catch (error) {
      this.handleConnectionError(error)
    } finally {
      this.connectionPromise = null
    }
  }

  private async processStream(response: Response): Promise<void> {
    const reader = response.body?.getReader()
    if (!reader) {
      throw new Error('No response body available for streaming')
    }

    const decoder = new TextDecoder()
    let buffer = ''

    try {
      while (true) {
        const { done, value } = await reader.read()

        if (done) {
          this.setConnectionState('disconnected')
          break
        }

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')
        buffer = lines.pop() || ''

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)

            if (data === '[DONE]') {
              this.setConnectionState('disconnected')
              return
            }

            const message = parseSSEMessage(data)
            if (message) {
              this.callbacks.onMessage(message)
            }
          }
        }
      }
    } catch (error) {
      console.error('❌ Error processing stream:', error)
      this.handleConnectionError(error)
    }
  }

  private handleConnectionError(error: unknown): void {
    const taskError = createTaskError(error, undefined, this.shouldReconnect())
    this.callbacks.onError(taskError)

    if (this.shouldReconnect()) {
      this.scheduleReconnect()
    } else {
      this.setConnectionState('error')
    }
  }

  private shouldReconnect(): boolean {
    return this.reconnectAttempts < this.options.maxReconnectAttempts
  }

  private scheduleReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
    }

    this.setConnectionState('reconnecting')
    this.reconnectAttempts++

    const delay = this.options.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1) // Exponential backoff

    this.reconnectTimer = setTimeout(() => {
      this.reconnectTimer = null
      this.connect()
    }, delay)
  }

  async reconnect(): Promise<void> {
    this.disconnect()
    this.reconnectAttempts = 0
    return this.connect()
  }

  disconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }

    this.setConnectionState('disconnected')
    this.connectionPromise = null
  }

  private setConnectionState(state: StreamingConnectionState): void {
    if (this.connectionState !== state) {
      this.connectionState = state
      this.callbacks.onConnectionChange(state)
    }
  }

  getConnectionState(): StreamingConnectionState {
    return this.connectionState
  }

  getReconnectAttempts(): number {
    return this.reconnectAttempts
  }

  isConnected(): boolean {
    return this.connectionState === 'connected'
  }
}
