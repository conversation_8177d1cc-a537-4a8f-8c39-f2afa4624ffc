/**
 * Authentication utilities for token management
 */

/**
 * Get the current auth token from localStorage
 * @returns JWT token string or null if not found
 */
export function getAuthToken(): string | null {
  try {
    const authData = localStorage.getItem('voicecode-auth-store')
    if (authData) {
      const parsed = JSON.parse(authData)
      const token = parsed.state?.token
      if (token) {
        return token
      }
    }
  } catch (error) {
    console.error('Failed to parse auth data from localStorage:', error)
  }
  
  return null
}

/**
 * Check if auth token is available
 * @returns boolean indicating if token exists
 */
export function hasAuthToken(): boolean {
  return getAuthToken() !== null
}

/**
 * Clear auth token from localStorage
 */
export function clearAuthToken(): void {
  try {
    localStorage.removeItem('voicecode-auth-store')
  } catch (error) {
    console.error('Failed to clear auth token:', error)
  }
}