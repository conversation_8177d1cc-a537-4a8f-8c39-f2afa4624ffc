/* eslint-disable @typescript-eslint/no-explicit-any */
// iOS platform optimizations require any type for CSS property extensions

import { PlatformUtils } from './platform';

export const iOSOptimizations = {
  // Disable bounce scrolling for better native feel
  disableBounceScrolling: () => {
    if (PlatformUtils.isIOS()) {
      (document.body.style as any).webkitOverflowScrolling = 'touch';
      (document.body.style as any).overflowScrolling = 'touch';
    }
  },

  // Optimize touch events
  optimizeTouchEvents: () => {
    if (PlatformUtils.isIOS()) {
      // Prevent zoom on double tap
      document.addEventListener('touchstart', (e) => {
        if (e.touches.length > 1) {
          e.preventDefault();
        }
      });

      // Prevent context menu on long press
      document.addEventListener('contextmenu', (e) => {
        e.preventDefault();
      });
    }
  },

  // Handle safe areas
  handleSafeAreas: () => {
    if (PlatformUtils.isIOS()) {
      const root = document.documentElement;
      root.style.setProperty('--status-bar-height', 'env(safe-area-inset-top)');
      root.style.setProperty('--home-indicator-height', 'env(safe-area-inset-bottom)');
    }
  }
};

// Initialize optimizations
if (PlatformUtils.isIOS()) {
  iOSOptimizations.disableBounceScrolling();
  iOSOptimizations.optimizeTouchEvents();
  iOSOptimizations.handleSafeAreas();
}