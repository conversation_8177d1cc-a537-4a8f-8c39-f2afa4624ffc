// Haptic feedback utilities for web

export type HapticPattern = 'light' | 'medium' | 'heavy' | 'success' | 'error' | 'warning';

class HapticFeedback {
  private isSupported: boolean;

  constructor() {
    this.isSupported = 'vibrate' in navigator;
  }

  private getPattern(type: HapticPattern): number | number[] {
    switch (type) {
      case 'light':
        return 10;
      case 'medium':
        return 20;
      case 'heavy':
        return 50;
      case 'success':
        return [100, 50, 100];
      case 'error':
        return [200, 100, 200, 100, 200];
      case 'warning':
        return [150, 75, 150];
      default:
        return 10;
    }
  }

  trigger(pattern: HapticPattern = 'light'): void {
    if (!this.isSupported) {
      return;
    }

    try {
      const vibrationPattern = this.getPattern(pattern);
      navigator.vibrate(vibrationPattern);
    } catch (error) {
      console.error('Haptic feedback failed:', error);
    }
  }

  // Quick convenience methods
  light(): void {
    this.trigger('light');
  }

  medium(): void {
    this.trigger('medium');
  }

  heavy(): void {
    this.trigger('heavy');
  }

  success(): void {
    this.trigger('success');
  }

  error(): void {
    this.trigger('error');
  }

  warning(): void {
    this.trigger('warning');
  }

  // Custom pattern
  custom(pattern: number | number[]): void {
    if (!this.isSupported) {
      return;
    }

    try {
      navigator.vibrate(pattern);
    } catch (error) {
      console.error('Custom haptic feedback failed:', error);
    }
  }

  // Stop all vibrations
  stop(): void {
    if (this.isSupported) {
      navigator.vibrate(0);
    }
  }

  // Check if haptic feedback is available
  isAvailable(): boolean {
    return this.isSupported;
  }
}

// Export singleton instance
export const haptic = new HapticFeedback();

// Hook for React components
import { useCallback } from 'react';

export function useHaptic() {
  const triggerHaptic = useCallback((pattern: HapticPattern = 'light') => {
    haptic.trigger(pattern);
  }, []);

  const triggerCustomHaptic = useCallback((pattern: number | number[]) => {
    haptic.custom(pattern);
  }, []);

  return {
    trigger: triggerHaptic,
    custom: triggerCustomHaptic,
    light: haptic.light.bind(haptic),
    medium: haptic.medium.bind(haptic),
    heavy: haptic.heavy.bind(haptic),
    success: haptic.success.bind(haptic),
    error: haptic.error.bind(haptic),
    warning: haptic.warning.bind(haptic),
    stop: haptic.stop.bind(haptic),
    isAvailable: haptic.isAvailable.bind(haptic),
  };
}

// Enhanced button component with haptic feedback will be in a separate component file

// Audio feedback alternative for devices without haptic support
export class AudioFeedback {
  private context: AudioContext | null = null;

  constructor() {
    if ('AudioContext' in window || 'webkitAudioContext' in window) {
      // @ts-expect-error - webkitAudioContext is not in TypeScript types but exists in Safari
      this.context = new (window.AudioContext || window.webkitAudioContext)();
    }
  }

  private beep(frequency: number, duration: number, volume: number = 0.1): void {
    if (!this.context) return;

    const oscillator = this.context.createOscillator();
    const gainNode = this.context.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(this.context.destination);

    oscillator.frequency.value = frequency;
    oscillator.type = 'sine';

    gainNode.gain.setValueAtTime(0, this.context.currentTime);
    gainNode.gain.linearRampToValueAtTime(volume, this.context.currentTime + 0.01);
    gainNode.gain.exponentialRampToValueAtTime(0.01, this.context.currentTime + duration);

    oscillator.start(this.context.currentTime);
    oscillator.stop(this.context.currentTime + duration);
  }

  light(): void {
    this.beep(800, 0.05);
  }

  medium(): void {
    this.beep(600, 0.1);
  }

  heavy(): void {
    this.beep(400, 0.15);
  }

  success(): void {
    this.beep(800, 0.1);
    setTimeout(() => this.beep(1000, 0.1), 120);
  }

  error(): void {
    this.beep(300, 0.2);
    setTimeout(() => this.beep(200, 0.2), 250);
  }

  warning(): void {
    this.beep(600, 0.1);
    setTimeout(() => this.beep(600, 0.1), 200);
  }
}

export const audio = new AudioFeedback();

// Combined feedback system
export function triggerFeedback(pattern: HapticPattern = 'light'): void {
  // Try haptic first
  if (haptic.isAvailable()) {
    haptic.trigger(pattern);
  } else {
    // Fallback to audio
    audio[pattern]?.();
  }
}