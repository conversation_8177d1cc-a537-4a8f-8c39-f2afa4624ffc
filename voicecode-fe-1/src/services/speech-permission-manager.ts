/**
 * Speech Permission Manager for VoiceCode
 * Handles speech recognition permissions across all platforms
 */

import { SpeechRecognition } from '@capacitor-community/speech-recognition';
import { Capacitor } from '@capacitor/core';

export interface PermissionStatus {
  granted: boolean;
  denied: boolean;
  prompt: boolean;
  restricted: boolean;
}

export class SpeechPermissionManager {
  private static instance: SpeechPermissionManager;
  private permissionStatus: PermissionStatus | null = null;

  static getInstance(): SpeechPermissionManager {
    if (!SpeechPermissionManager.instance) {
      SpeechPermissionManager.instance = new SpeechPermissionManager();
    }
    return SpeechPermissionManager.instance;
  }

  async checkPermissions(): Promise<PermissionStatus> {
    const platform = Capacitor.getPlatform();
    
    if (platform === 'web') {
      // Handle web platform with browser Speech Recognition API
      return this.checkWebPermissions();
    }
    
    try {
      const result = await SpeechRecognition.checkPermissions();
      
      const status: PermissionStatus = {
        granted: result.speechRecognition === 'granted',
        denied: result.speechRecognition === 'denied',
        prompt: result.speechRecognition === 'prompt',
        restricted: false, // Not used in this context
      };

      this.permissionStatus = status;
      return status;
    } catch (error) {
      console.error('Error checking permissions:', error);
      
      // Fallback to web permissions if native fails
      if (platform !== 'ios' && platform !== 'android') {
        return this.checkWebPermissions();
      }
      
      throw error;
    }
  }

  private async checkWebPermissions(): Promise<PermissionStatus> {
    try {
      // Check if browser supports Speech Recognition
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      
      if (!SpeechRecognition) {
        const status: PermissionStatus = {
          granted: false,
          denied: true,
          prompt: false,
          restricted: true
        };
        this.permissionStatus = status;
        return status;
      }

      // Check microphone permissions via navigator.mediaDevices
      if ('mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices) {
        try {
          const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
          // Permission granted, clean up stream
          stream.getTracks().forEach(track => track.stop());
          const status: PermissionStatus = {
            granted: true,
            denied: false,
            prompt: false,
            restricted: false
          };
          this.permissionStatus = status;
          return status;
        } catch (error: any) {
          if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
            const status: PermissionStatus = {
              granted: false,
              denied: true,
              prompt: false,
              restricted: false
            };
            this.permissionStatus = status;
            return status;
          } else if (error.name === 'NotFoundError') {
            const status: PermissionStatus = {
              granted: false,
              denied: false,
              prompt: false,
              restricted: true
            };
            this.permissionStatus = status;
            return status;
          } else {
            // Permission needs to be requested
            const status: PermissionStatus = {
              granted: false,
              denied: false,
              prompt: true,
              restricted: false
            };
            this.permissionStatus = status;
            return status;
          }
        }
      }

      // Fallback - assume permission needs to be requested
      const status: PermissionStatus = {
        granted: false,
        denied: false,
        prompt: true,
        restricted: false
      };
      this.permissionStatus = status;
      return status;
    } catch (error) {
      console.error('Error checking web permissions:', error);
      const status: PermissionStatus = {
        granted: false,
        denied: true,
        prompt: false,
        restricted: false
      };
      this.permissionStatus = status;
      return status;
    }
  }

  async requestPermissions(): Promise<PermissionStatus> {
    const platform = Capacitor.getPlatform();
    
    if (platform === 'web') {
      // For web, requesting permissions is the same as checking them
      // because getUserMedia will trigger the permission prompt
      return this.checkWebPermissions();
    }
    
    try {
      await SpeechRecognition.requestPermissions();
      return this.checkPermissions(); // Re-check after request
    } catch (error) {
      console.error('Error requesting permissions:', error);
      
      // Fallback to web permissions if native fails
      if (platform !== 'ios' && platform !== 'android') {
        return this.checkWebPermissions();
      }
      
      throw error;
    }
  }

  async ensurePermissions(): Promise<boolean> {
    const status = await this.checkPermissions();
    
    if (status.granted) {
      return true;
    }

    if (status.prompt) {
      const newStatus = await this.requestPermissions();
      return newStatus.granted;
    }

    return false;
  }

  getLastKnownStatus(): PermissionStatus | null {
    return this.permissionStatus;
  }

  /**
   * Check if speech recognition is available on the current platform
   */
  async isAvailable(): Promise<boolean> {
    const platform = Capacitor.getPlatform();
    
    if (platform === 'web') {
      // Check if browser supports Speech Recognition API
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const result = !!SpeechRecognition && 'mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices;
      return result;
    }
    
    try {
      const { available } = await SpeechRecognition.available();
      return available;
    } catch (error) {
      console.error('Error checking availability:', error);
      
      // Fallback to web check if native fails
      if (platform !== 'ios' && platform !== 'android') {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        return !!SpeechRecognition && 'mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices;
      }
      
      return false;
    }
  }

  /**
   * Get platform-specific guidance for handling denied permissions
   */
  getDeniedPermissionGuidance(): string {
    const platform = Capacitor.getPlatform();
    
    switch (platform) {
      case 'ios':
        return `Speech recognition access is required for VoiceCode.
Please enable it in Settings:
1. Open Settings
2. Find VoiceCode
3. Enable Speech Recognition & Microphone`;
      
      case 'android':
        return `VoiceCode needs microphone access to:
• Convert your voice commands to text
• Execute code operations hands-free
• Improve developer productivity`;
      
      case 'web':
        return `Your browser needs microphone access to use voice commands.
Please allow microphone access when prompted.`;
      
      default:
        return 'Microphone access is required for voice commands.';
    }
  }

  /**
   * Handle permission denial based on platform
   */
  async handlePermissionDenied(): Promise<void> {
    Capacitor.getPlatform();
    this.getDeniedPermissionGuidance();
    
    // Log for debugging
    
    // Platform-specific handling could be added here
    // For now, we'll just provide the guidance message
  }
}

/**
 * Convenience function to get the singleton instance
 */
export const speechPermissionManager = SpeechPermissionManager.getInstance();