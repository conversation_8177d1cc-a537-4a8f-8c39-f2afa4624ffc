import { Preferences } from '@capacitor/preferences';
import { Capacitor } from '@capacitor/core';
import type { AuthUser } from '@/types/auth.types';

export class SecureTokenManager {
  private static readonly TOKEN_KEY = 'oauth_access_token';
  private static readonly USER_KEY = 'oauth_user_data';

  static async storeTokens(accessToken: string, user: AuthUser): Promise<void> {
    if (Capacitor.isNativePlatform()) {
      await Promise.all([
        Preferences.set({
          key: this.TOKEN_KEY,
          value: accessToken
        }),
        Preferences.set({
          key: this.USER_KEY,
          value: JSON.stringify(user)
        })
      ]);
    } else {
      // Fallback to localStorage for web
      localStorage.setItem(this.TOKEN_KEY, accessToken);
      localStorage.setItem(this.USER_KEY, JSON.stringify(user));
    }
  }

  static async getStoredTokens(): Promise<{token: string | null, user: AuthUser | null}> {
    if (Capacitor.isNativePlatform()) {
      
      const [tokenResult, userResult] = await Promise.all([
        Preferences.get({ key: this.TOKEN_KEY }),
        Preferences.get({ key: this.USER_KEY })
      ]);
      
      const token = tokenResult.value;
      const user = userResult.value ? JSON.parse(userResult.value) : null;

      
      return { token, user };
    } else {
      // Fallback to localStorage for web
      const token = localStorage.getItem(this.TOKEN_KEY);
      const userStr = localStorage.getItem(this.USER_KEY);
      const user = userStr ? JSON.parse(userStr) : null;
      
      return { token, user };
    }
  }

  static async clearStoredTokens(): Promise<void> {
    if (Capacitor.isNativePlatform()) {
      
      await Promise.all([
        Preferences.remove({ key: this.TOKEN_KEY }),
        Preferences.remove({ key: this.USER_KEY })
      ]);
      
    } else {
      // Fallback to localStorage for web
      localStorage.removeItem(this.TOKEN_KEY);
      localStorage.removeItem(this.USER_KEY);
    }
  }

  static async hasStoredTokens(): Promise<boolean> {
    const { token, user } = await this.getStoredTokens();
    return !!(token && user);
  }
}