import { apiClient } from './api'
import type {
  SendChatMessageResponse,
  ChatHistoryRequest,
  ChatHistoryResponse,
  ClearChatHistoryResponse
} from '@/types/chat.types'
import type {
  CommandExecuteRequest,
  CommandExecuteResponse
} from '@/types/command.types'

export class ChatService {
  static readonly ENDPOINTS = {
    SEND_MESSAGE: (sandboxId: string) => `/api/sandbox/${sandboxId}/chat`,
    CHAT_HISTORY: (sandboxId: string) => `/api/sandbox/${sandboxId}/chat/history`,
    CLEAR_HISTORY: (sandboxId: string) => `/api/sandbox/${sandboxId}/chat`,
    EXECUTE_COMMAND: (sandboxId: string) => `/api/sandbox/${sandboxId}/execute`
  }

  static readonly DEFAULT_PAGE_SIZE = 50

  /**
   * Send a chat message
   */
  static async sendMessage(
    sandboxId: string,
    message: string,
    messageType: 'user' | 'system' | 'error' | 'status' = 'user',
    metadata: Record<string, any> = {}
  ): Promise<SendChatMessageResponse> {
    try {
      const request = {
        message: message.trim(),
        message_type: messageType,  // Convert camelCase to snake_case for backend
        metadata: metadata
      }

      const response: any = await apiClient.post(
        this.ENDPOINTS.SEND_MESSAGE(sandboxId),
        request
      )

      return response.data as SendChatMessageResponse
    } catch (error) {
      console.error(`Failed to send message to sandbox ${sandboxId}:`, error)
      throw new Error('Failed to send message')
    }
  }

  /**
   * Load chat history with pagination
   */
  static async loadHistory(
    sandboxId: string,
    options: ChatHistoryRequest = {}
  ): Promise<ChatHistoryResponse> {
    try {
      const params = {
        page: options.page || 1,
        pageSize: options.pageSize || this.DEFAULT_PAGE_SIZE,
        ...(options.messageType && { messageType: options.messageType })
      }

      const response: any = await apiClient.get(
        this.ENDPOINTS.CHAT_HISTORY(sandboxId),
        { params }
      )

      const historyData = response.data as ChatHistoryResponse

      // Ensure dates are properly parsed and convert snake_case to camelCase
      const messages = historyData.messages.map((msg: any) => ({
        ...msg,
        messageType: msg.message_type || msg.messageType,
        sandboxId: msg.sandbox_id || msg.sandboxId,
        userId: msg.user_id || msg.userId,
        commandId: msg.command_id || msg.commandId,
        createdAt: new Date(msg.created_at || msg.createdAt)
      }))

      return {
        ...historyData,
        messages
      }
    } catch (error) {
      console.error(`Failed to load chat history for sandbox ${sandboxId}:`, error)
      throw new Error('Failed to load chat history')
    }
  }

  /**
   * Execute a command in the sandbox
   */
  static async executeCommand(
    sandboxId: string,
    command: string,
    workingDirectory?: string
  ): Promise<CommandExecuteResponse> {
    try {
      const request: CommandExecuteRequest = {
        command: command.trim(),
        working_directory: workingDirectory || '/home/<USER>/workspace/repository'
      }

      const response: any = await apiClient.post(
        this.ENDPOINTS.EXECUTE_COMMAND(sandboxId),
        request
      )

      return response.data as CommandExecuteResponse
    } catch (error) {
      console.error(`Failed to execute command in sandbox ${sandboxId}:`, error)
      throw new Error('Failed to execute command')
    }
  }

  /**
   * Clear chat history
   */
  static async clearHistory(sandboxId: string): Promise<ClearChatHistoryResponse> {
    try {
      const response: any = await apiClient.delete(
        this.ENDPOINTS.CLEAR_HISTORY(sandboxId)
      )
      return response.data as ClearChatHistoryResponse
    } catch (error) {
      console.error(`Failed to clear chat history for sandbox ${sandboxId}:`, error)
      throw new Error('Failed to clear chat history')
    }
  }

  /**
   * Check if a message is a command that should be executed
   */
  static isExecutableCommand(message: string): boolean {
    const trimmed = message.trim()
    
    if (!trimmed) return false
    
    // Non-command patterns
    const nonCommandPatterns = [
      /^(hi|hello|hey|thanks|thank you|bye|goodbye)$/i,
      /^(yes|no|ok|okay)$/i,
      /^\?+$/,
    ]
    
    return !nonCommandPatterns.some(pattern => pattern.test(trimmed))
  }

  /**
   * Format command output for display
   */
  static formatCommandOutput(response: CommandExecuteResponse): {
    content: string
    isError: boolean
  } {
    const { exit_code, result } = response
    
    return {
      content: result || '',
      isError: exit_code !== 0
    }
  }
}