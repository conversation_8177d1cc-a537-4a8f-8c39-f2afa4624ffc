/**
 * React hook for managing speech recognition permissions
 */

import { useState, useEffect, useCallback } from 'react';
import { speechPermissionManager, type PermissionStatus } from '../services/speech-permission-manager';

interface UseSpeechPermissionResult {
  status: PermissionStatus | null;
  isChecking: boolean;
  isGranted: boolean;
  isAvailable: boolean;
  checkPermissions: () => Promise<void>;
  requestPermissions: () => Promise<boolean>;
  ensurePermissions: () => Promise<boolean>;
  handleDenied: () => Promise<void>;
  guidanceMessage: string;
}

export const useSpeechPermission = (): UseSpeechPermissionResult => {
  const [status, setStatus] = useState<PermissionStatus | null>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [isAvailable, setIsAvailable] = useState(false);

  const checkPermissions = useCallback(async () => {
    setIsChecking(true);
    try {
      const newStatus = await speechPermissionManager.checkPermissions();
      setStatus(newStatus);
    } catch (error) {
      console.error('Permission check failed:', error);
    } finally {
      setIsChecking(false);
    }
  }, []);

  const requestPermissions = useCallback(async (): Promise<boolean> => {
    setIsChecking(true);
    try {
      const result = await speechPermissionManager.requestPermissions();
      setStatus(result);
      return result.granted;
    } catch (error) {
      console.error('Permission request failed:', error);
      return false;
    } finally {
      setIsChecking(false);
    }
  }, []);

  const ensurePermissions = useCallback(async (): Promise<boolean> => {
    setIsChecking(true);
    try {
      const result = await speechPermissionManager.ensurePermissions();
      // Refresh status after ensuring permissions
      await checkPermissions();
      return result;
    } catch (error) {
      console.error('Ensure permissions failed:', error);
      return false;
    } finally {
      setIsChecking(false);
    }
  }, [checkPermissions]);

  const handleDenied = useCallback(async () => {
    await speechPermissionManager.handlePermissionDenied();
  }, []);

  const checkAvailability = useCallback(async () => {
    try {
      const available = await speechPermissionManager.isAvailable();
      setIsAvailable(available);
    } catch (error) {
      console.error('Availability check failed:', error);
      setIsAvailable(false);
    }
  }, []);

  // Check permissions and availability on mount
  useEffect(() => {
    const initialize = async () => {
      await Promise.all([
        checkPermissions(),
        checkAvailability()
      ]);
    };
    
    initialize();
  }, [checkPermissions, checkAvailability]);

  // Get platform-specific guidance message
  const guidanceMessage = speechPermissionManager.getDeniedPermissionGuidance();

  return {
    status,
    isChecking,
    isGranted: status?.granted || false,
    isAvailable,
    checkPermissions,
    requestPermissions,
    ensurePermissions,
    handleDenied,
    guidanceMessage,
  };
};