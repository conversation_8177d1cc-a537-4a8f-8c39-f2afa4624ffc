import { useState, useEffect } from 'react';

interface PushNotificationState {
  isSupported: boolean;
  permission: NotificationPermission;
  isSubscribed: boolean;
  subscription: PushSubscription | null;
  error: string | null;
}

export function usePushNotifications() {
  const [state, setState] = useState<PushNotificationState>({
    isSupported: 'Notification' in window && 'serviceWorker' in navigator && 'PushManager' in window,
    permission: 'default',
    isSubscribed: false,
    subscription: null,
    error: null,
  });

  useEffect(() => {
    if (!state.isSupported) {
      return;
    }

    // Check current permission status
    setState(prev => ({ ...prev, permission: Notification.permission }));

    // Check if already subscribed
    checkSubscription();
  }, [state.isSupported]);

  const checkSubscription = async () => {
    try {
      const registration = await navigator.serviceWorker.ready;
      const subscription = await registration.pushManager.getSubscription();
      
      setState(prev => ({
        ...prev,
        isSubscribed: !!subscription,
        subscription,
      }));
    } catch (error) {
      console.error('Error checking push subscription:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to check subscription',
      }));
    }
  };

  const requestPermission = async (): Promise<boolean> => {
    if (!state.isSupported) {
      setState(prev => ({ ...prev, error: 'Push notifications not supported' }));
      return false;
    }

    try {
      const permission = await Notification.requestPermission();
      setState(prev => ({ ...prev, permission }));
      
      return permission === 'granted';
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Permission request failed',
      }));
      return false;
    }
  };

  const subscribe = async (vapidKey?: string): Promise<PushSubscription | null> => {
    if (!state.isSupported) {
      setState(prev => ({ ...prev, error: 'Push notifications not supported' }));
      return null;
    }

    try {
      const registration = await navigator.serviceWorker.ready;
      
      const options: PushSubscriptionOptions = {
        userVisibleOnly: true,
        applicationServerKey: vapidKey ? new TextEncoder().encode(vapidKey).buffer : null,
      };

      const subscription = await registration.pushManager.subscribe(options);
      
      setState(prev => ({
        ...prev,
        isSubscribed: true,
        subscription,
        error: null,
      }));

      return subscription;
    } catch (error) {
      console.error('Error subscribing to push notifications:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Subscription failed',
      }));
      return null;
    }
  };

  const unsubscribe = async (): Promise<boolean> => {
    if (!state.subscription) {
      return false;
    }

    try {
      await state.subscription.unsubscribe();
      setState(prev => ({
        ...prev,
        isSubscribed: false,
        subscription: null,
        error: null,
      }));
      
      return true;
    } catch (error) {
      console.error('Error unsubscribing from push notifications:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Unsubscribe failed',
      }));
      return false;
    }
  };

  const sendTestNotification = async (title: string, body: string) => {
    if (state.permission !== 'granted') {
      setState(prev => ({ ...prev, error: 'Permission not granted' }));
      return;
    }

    try {
      const registration = await navigator.serviceWorker.ready;
      await registration.showNotification(title, {
        body,
        icon: '/icons/icon-192x192.png',
        badge: '/icons/icon-72x72.png',
        tag: 'test-notification',
        requireInteraction: false,
      });
    } catch (error) {
      console.error('Error sending test notification:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to send notification',
      }));
    }
  };

  return {
    ...state,
    requestPermission,
    subscribe,
    unsubscribe,
    sendTestNotification,
    checkSubscription,
  };
}

// Hook for handling notification interactions
export function useNotificationHandler() {
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data.type === 'NOTIFICATION_REPLY') {
        // Handle notification reply action
        // Navigate to conversation and open reply
        window.history.pushState({}, '', `/chat/${event.data.data.conversationId}`);
      } else if (event.data.type === 'NOTIFICATION_VIEW') {
        // Handle notification view action
        // Navigate to conversation
        window.history.pushState({}, '', `/chat/${event.data.data.conversationId}`);
      }
    };

    navigator.serviceWorker?.addEventListener('message', handleMessage);

    return () => {
      navigator.serviceWorker?.removeEventListener('message', handleMessage);
    };
  }, []);
}

// Utility function to convert VAPID key
export function urlBase64ToUint8Array(base64String: string): Uint8Array {
  const padding = '='.repeat((4 - base64String.length % 4) % 4);
  const base64 = (base64String + padding)
    .replace(/-/g, '+')
    .replace(/_/g, '/');

  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}