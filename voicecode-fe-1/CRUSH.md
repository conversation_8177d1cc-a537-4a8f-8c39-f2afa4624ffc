# CRUSH.md

## Commands
- Dev: pnpm run dev
- Build: pnpm run build | Preview: pnpm run preview
- Lint: pnpm run lint | Type Check: pnpm run type-check
- Prod Build: pnpm run build:prod
- Capacitor: cap:sync | cap:ios | cap:android | dev:ios | dev:android | cap:clean
- Single test: none configured in this workspace; use repo-level pytest for backend (see root CRUSH.md)

## Code style
- Language: TypeScript strict; React 19 with functional components/hooks
- Imports: external → internal (@/) → relative; prefer @/ for src
- Naming: camelCase vars/functions; PascalCase components/types; kebab-case files
- Types: separate interfaces; use type for unions; explicit return types
- State: Zustand with devtools/persist; split State/Actions interfaces
- UI: Radix UI + Tailwind; use cn() for className merges; mobile-first
- Errors: try/catch; narrow error types; console logs with emojis for debug only
- Formatting/Lint: ESLint via eslint.config.js; follow import order
- Routing: React Router; colocate route components under src
- Capacitor: Keep web build in ios/App/public; sync via pnpm run cap:sync

## Notes for agents
- Use @/ alias. No comments unless asked. Do not commit unless requested. Cursor/Copilot rules: none present.
