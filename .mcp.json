{"mcpServers": {"sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "alwaysAllow": ["sequentialthinking"]}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": ""}}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}}}