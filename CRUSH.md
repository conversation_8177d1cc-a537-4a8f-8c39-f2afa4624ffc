# CRUSH.md

## Commands (pnpm, turbo monorepo)
- Dev: pnpm run dev
- Dev (with backend): pnpm run dev:pwa
- Build: pnpm run build
- Lint: pnpm run lint | Auto-fix: pnpm run lint:fix
- Type check: pnpm run type-check
- Format: pnpm run format | Check: pnpm run format:check
- Test (all): pnpm run test | Run: pnpm run test:run | Coverage: pnpm run test:coverage
- Single test: use workspace script directly (no global test framework). For backend: cd voicecode/fastapi-app && uv run pytest path::TestClass::test_name
- Docker backend: pnpm run docker:backend | Down: pnpm run docker:down
- Mobile (iOS/Android): cd voicecode-fe-1 && pnpm run cap:ios | pnpm run cap:android

## Frontend workspace: voicecode-fe-1
- Build: pnpm -w --filter voicecode-fe-1 run build
- Type/Lint: pnpm -w --filter voicecode-fe-1 run type-check | lint
- Capacitor sync/open: pnpm run cap:sync | cap:ios | cap:android

## Code style
- Language: TypeScript strict; React 19 with functional components and hooks
- Imports: external → internal (@/) → relative; prefer @/ for src
- Naming: camelCase variables/functions; PascalCase components/types; kebab-case files
- Types: separate interfaces; use type for unions; explicit return types; avoid any
- State: Zustand stores with devtools/persist; separate State/Actions types
- UI: Radix UI + Tailwind; use cn() for className merges; mobile-first
- Errors: try/catch; narrow error types; no silent failures; use console logs with emojis for debug only
- Formatting/Lint: ESLint (eslint.config.js); follow import order and no unused vars
- Routing: React Router; colocate route components under src
- Security: never commit secrets; do not log tokens; use secure storage utilities where provided

## Backend (voicecode/fastapi-app)
- Use uv virtualenv; run tests/migrations via uv (see AGENTS.md for migration commands)
- Streaming/task services covered by pytest; prefer pytest -k "pattern" for single test selection

## Notes for agents
- Never use npm; use pnpm. Do not run the app for users. Avoid adding comments unless asked. Do not commit unless requested. Include Cursor/Copilot rules here if added later.
