from fastapi import <PERSON><PERSON><PERSON>, HTT<PERSON>Exception, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import RedirectResponse
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
from fastapi.exceptions import HTTPException as FastAPIHTTPException
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
from pydantic import BaseModel, Field, EmailStr
from pydantic_settings import BaseSettings
from typing import Optional, List, Dict, Any
import asyncio
import httpx
import redis
import json
import jwt
import os
from datetime import datetime, timedelta, timezone
import uuid
import logging
from daytona import Daytona, DaytonaConfig, CreateSandboxFromSnapshotParams, Resources
from fastapi import BackgroundTasks
import threading
import time
from sqlalchemy.orm import Session
from database import get_db, ChatMessage, WaitlistEntry, Task, init_database, test_database_connection
from services import TaskService, CommandProcessor
from services.daytona_service import DaytonaService
from routers import streaming
import dependencies

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Settings
class Settings(BaseSettings):
    # GitHub App
    github_app_id: str = Field(..., env="GITHUB_APP_ID")
    github_app_name: Optional[str] = Field(None, env="GITHUB_APP_NAME")
    github_private_key: str = Field(..., env="GITHUB_PRIVATE_KEY")
    github_webhook_secret: str = Field(..., env="GITHUB_WEBHOOK_SECRET")
    # GitHub OAuth for mobile (existing)
    github_oauth_client_id: str = Field(..., env="GITHUB_OAUTH_CLIENT_ID")
    github_oauth_client_secret: str = Field(..., env="GITHUB_OAUTH_CLIENT_SECRET")
    
    # GitHub OAuth for web
    github_oauth_web_client_id: str = Field(..., env="GITHUB_OAUTH_WEB_CLIENT_ID")
    github_oauth_web_client_secret: str = Field(..., env="GITHUB_OAUTH_WEB_CLIENT_SECRET")
    
    # Daytona
    daytona_api_key: str = Field(..., env="DAYTONA_API_KEY")
    daytona_api_url: str = Field(..., env="DAYTONA_API_URL")
    daytona_target: str = Field("us", env="DAYTONA_TARGET")
    daytona_org_id: Optional[str] = Field(None, env="DAYTONA_ORG_ID")
    daytona_image: str = Field("voicecode-claude:1.0.1", env="DAYTONA_IMAGE")
    
    # Redis
    redis_url: str = Field("redis://localhost:6379", env="REDIS_URL")
    
    # JWT
    jwt_secret: str = Field(..., env="JWT_SECRET")
    jwt_algorithm: str = Field("HS256", env="JWT_ALGORITHM")
    
    # App settings
    app_url: str = Field("http://localhost:9100", env="APP_URL")
    frontend_url: str = Field("http://localhost:5173", env="FRONTEND_URL")
    environment: str = Field("development", env="ENVIRONMENT")
    
    class Config:
        env_file = ".env"

settings = Settings()

# Initialize FastAPI app
app = FastAPI(
    title="VoiceCode API",
    description="Unified API for GitHub authentication and Daytona sandbox management",
    version="1.0.0"
)

# Initialize rate limiter with error handling
try:
    limiter = Limiter(key_func=get_remote_address)
    app.state.limiter = limiter
    app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)
    logger.info("✅ Rate limiter initialized successfully")
except Exception as e:
    logger.error(f"❌ Failed to initialize rate limiter: {e}")
    # Create a dummy limiter that doesn't enforce limits
    class DummyLimiter:
        def limit(self, rate_string):
            def decorator(func):
                return func
            return decorator
    limiter = DummyLimiter()
    app.state.limiter = limiter
    logger.warning("⚠️ Using dummy rate limiter due to initialization failure")

# CORS middleware - configured for frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        settings.frontend_url,  # Frontend URL from environment
        "http://localhost:5173",
        "http://localhost:9080", 
        "http://localhost:9101",  # Landing page port
        "http://localhost:4173",  # Vite preview port
        "http://localhost:3000",  # Common dev port
        "capacitor://localhost",
        "ionic://localhost",
        "http://localhost"
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
    expose_headers=["*"],
)

# Initialize Redis with error handling
try:
    redis_client = redis.Redis.from_url(settings.redis_url, decode_responses=True)
    # Test the connection with timeout
    redis_client.ping()
    logger.info(f"✅ Redis connected successfully at {settings.redis_url}")
except redis.ConnectionError as e:
    logger.error(f"❌ Redis connection failed: {e}")
    logger.warning("⚠️ Using in-memory fallback for temporary storage")
    redis_client = None
except redis.TimeoutError as e:
    logger.error(f"❌ Redis connection timeout: {e}")
    logger.warning("⚠️ Using in-memory fallback for temporary storage")
    redis_client = None
except Exception as e:
    logger.error(f"❌ Unexpected Redis error: {e}")
    logger.warning("⚠️ Using in-memory fallback for temporary storage")
    redis_client = None

# In-memory fallback for when Redis is not available
temp_storage = {} if redis_client is None else None

# Helper functions for storage (Redis or in-memory)
def store_data(key: str, value: str, expire_seconds: int = 300):
    """Store data with expiration"""
    if redis_client:
        redis_client.setex(key, expire_seconds, value)
    else:
        # In-memory storage with expiration
        import time
        temp_storage[key] = {
            "value": value,
            "expires_at": time.time() + expire_seconds
        }

def get_data(key: str) -> Optional[str]:
    """Get data from storage"""
    if redis_client:
        return redis_client.get(key)
    else:
        # In-memory storage with expiration check
        import time
        if key in temp_storage:
            if time.time() < temp_storage[key]["expires_at"]:
                return temp_storage[key]["value"]
            else:
                # Expired, remove it
                del temp_storage[key]
        return None

def delete_data(key: str):
    """Delete data from storage"""
    if redis_client:
        redis_client.delete(key)
    else:
        temp_storage.pop(key, None)

def get_keys(pattern: str) -> List[str]:
    """Get keys matching pattern"""
    if redis_client:
        return redis_client.keys(pattern)
    else:
        import fnmatch
        return [key for key in temp_storage.keys() if fnmatch.fnmatch(key, pattern)]

# Rate limiting is now handled by slowapi

# Disposable email domains blacklist (common ones)
DISPOSABLE_EMAIL_DOMAINS = {
    '10minutemail.com', '10minutemail.net', '10minutemail.org', 'guerrillamail.com',
    'guerrillamail.net', 'guerrillamail.org', 'guerrillamailblock.com', 'spam4.me',
    'tempmail.org', 'temp-mail.org', 'throwaway.email', 'mailinator.com',
    'maildrop.cc', 'yopmail.com', '33mail.com', 'getnada.com', 'trashmail.com',
    'tempr.email', 'dispostable.com', 'fakeinbox.com', 'mohmal.com', 'sharklasers.com'
}

def is_disposable_email(email: str) -> bool:
    """Check if email is from a disposable email service"""
    domain = email.split('@')[-1].lower()
    return domain in DISPOSABLE_EMAIL_DOMAINS

def has_valid_mx_record(domain: str) -> bool:
    """Check if domain has valid MX record (basic DNS validation)"""
    try:
        import dns.resolver
        # Try to resolve MX record
        mx_records = dns.resolver.resolve(domain, 'MX')
        return len(mx_records) > 0
    except ImportError:
        logger.warning("dnspython not available, skipping MX record validation")
        return True
    except Exception as e:
        # If DNS lookup fails, assume it's valid (could be network issue)
        logger.debug(f"DNS lookup failed for {domain}: {e}")
        return True

def validate_email_content(email: str) -> tuple[bool, str]:
    """Validate email for suspicious patterns"""
    email_lower = email.lower()
    
    # Check for repeated characters (like <EMAIL>)
    local_part = email.split('@')[0]
    if len(set(local_part)) == 1 and len(local_part) > 3:
        return False, "Email contains suspicious repeated characters"
    
    # Check for common spam patterns
    spam_patterns = ['test', 'spam', 'fake', 'noreply', 'donotreply']
    if any(pattern in email_lower for pattern in spam_patterns):
        return False, "Email contains suspicious keywords"
    
    # Check for excessive length
    if len(email) > 254:  # RFC standard
        return False, "Email address too long"
    
    return True, ""

def validate_email_security(email: str) -> tuple[bool, str]:
    """Comprehensive email validation for security"""
    # Check if disposable
    if is_disposable_email(email):
        return False, "Disposable email addresses are not allowed"
    
    # Validate content
    is_valid_content, content_error = validate_email_content(email)
    if not is_valid_content:
        return False, content_error
    
    # Check MX record
    domain = email.split('@')[-1]
    if not has_valid_mx_record(domain):
        return False, "Email domain does not exist"
    
    return True, ""

# Initialize Daytona SDK with error handling
try:
    daytona_config_dict = {
        "api_key": settings.daytona_api_key,
        "api_url": settings.daytona_api_url,
        "target": settings.daytona_target,
        "organization_id": settings.daytona_org_id,
    }
    daytona_config = DaytonaConfig(**daytona_config_dict)
    daytona = Daytona(daytona_config)
    
    # Create DaytonaService instance for streaming capabilities
    daytona_service = DaytonaService(daytona_config)
    logger.info("✅ Daytona SDK and DaytonaService initialized successfully")
except Exception as e:
    logger.error(f"❌ Failed to initialize Daytona SDK: {e}")
    # Create a dummy daytona object to prevent errors
    class DummyDaytona:
        def create(self, *args, **kwargs):
            raise HTTPException(status_code=503, detail="Daytona service unavailable")
        def get(self, *args, **kwargs):
            raise HTTPException(status_code=503, detail="Daytona service unavailable")
        def delete(self, *args, **kwargs):
            raise HTTPException(status_code=503, detail="Daytona service unavailable")
    
    class DummyDaytonaService:
        def __init__(self):
            pass
        def execute_command_stream(self, *args, **kwargs):
            raise HTTPException(status_code=503, detail="Daytona service unavailable")
        def execute_command(self, *args, **kwargs):
            raise HTTPException(status_code=503, detail="Daytona service unavailable")
    
    daytona = DummyDaytona()
    daytona_service = DummyDaytonaService()
    logger.warning("⚠️ Using dummy Daytona SDK and service due to initialization failure")

# Initialize CommandProcessor
command_processor = CommandProcessor()
logger.info("✅ CommandProcessor initialized successfully")

# Background task for syncing sandbox statuses
def sync_sandbox_statuses():
    """Background task to sync sandbox statuses from Daytona SDK"""
    while True:
        try:
            if redis_client:
                # Get all sandbox keys
                sandbox_keys = redis_client.keys("sandbox:*")
                logger.info(f"🔄 Syncing {len(sandbox_keys)} sandbox statuses")
                
                for key in sandbox_keys:
                    try:
                        sandbox_data = redis_client.get(key)
                        if sandbox_data:
                            sandbox_info = json.loads(sandbox_data)
                            sandbox_id = sandbox_info.get("sandbox_id")
                            
                            if sandbox_id:
                                # Get current status from Daytona SDK
                                daytona_sandbox = daytona.get(sandbox_id)
                                current_state = daytona_sandbox.state
                                
                                # Update if status changed
                                if sandbox_info.get("status") != current_state:
                                    logger.info(f"📊 Sandbox {sandbox_id} status changed: {sandbox_info.get('status')} -> {current_state}")
                                    sandbox_info["status"] = current_state
                                    sandbox_info["last_synced"] = datetime.now().isoformat()
                                    
                                    # Update Redis
                                    redis_client.setex(
                                        key,
                                        86400,  # 24 hours
                                        json.dumps(sandbox_info)
                                    )
                                    
                    except Exception as e:
                        logger.warning(f"Failed to sync sandbox {key}: {e}")
                        
        except Exception as e:
            logger.error(f"Error in sync_sandbox_statuses: {e}")
        
        # Sleep for 30 seconds before next sync
        time.sleep(30)

# Initialize database
try:
    if test_database_connection():
        init_database()
        logger.info("✅ Database initialized successfully")
    else:
        logger.warning("⚠️ Database connection failed - chat features may not work")
except Exception as e:
    logger.error(f"❌ Database initialization failed: {e}")

# Start background sync task
if redis_client:
    # sync_thread = threading.Thread(target=sync_sandbox_statuses, daemon=True)
    # sync_thread.start()
    # logger.info("🚀 Started background sandbox status sync task")
    logger.info("⏸️ Sandbox status sync task disabled (temporary)")

# Set up shared dependencies
dependencies.set_shared_dependencies(
    redis_client=redis_client,
    daytona_service=daytona_service,
    limiter=limiter,
    settings=settings
)

# Register routers
app.include_router(streaming.router)
logger.info("✅ Registered streaming router")

# Security
security = HTTPBearer()

# Pydantic models
class GitHubAuthResponse(BaseModel):
    access_token: str
    token_type: str
    expires_in: int
    user: Dict[str, Any]

class MobileTokenExchangeRequest(BaseModel):
    authorization_code: str
    redirect_uri: str

class SandboxConfig(BaseModel):
    cpu: Optional[int] = Field(None, description="Number of CPU cores")
    memory: Optional[int] = Field(None, description="Memory in GB")
    storage: Optional[int] = Field(None, description="Storage in GB")
    environment_variables: Optional[Dict[str, str]] = Field(None, description="Environment variables")
    language: Optional[str] = Field(None, description="Programming language (e.g., python, typescript, go)")
    auto_stop_interval: Optional[int] = Field(15, description="Auto-stop interval in minutes (0 to disable)")
    auto_archive_interval: Optional[int] = Field(None, description="Auto-archive interval in minutes")
    labels: Optional[Dict[str, str]] = Field(None, description="Custom labels for the sandbox")

class SandboxCreateRequest(BaseModel):
    repo_url: str = Field(..., description="GitHub repository URL")
    branch: str = Field("main", description="Branch to checkout")
    github_token: Optional[str] = Field(None, description="GitHub access token")
    claude_code_oauth_token: Optional[str] = Field(None, description="Claude Code OAuth token")
    sandbox_name: Optional[str] = Field(None, description="Custom sandbox name")
    config: Optional[SandboxConfig] = Field(None, description="Sandbox configuration")

class SandboxResponse(BaseModel):
    sandbox_id: str
    sandbox_name: str
    repo_url: str
    branch: str
    status: str
    created_at: str

class CommandRequest(BaseModel):
    command: str = Field(..., description="Command to execute")
    working_directory: str = Field("/home/<USER>/workspace/repository", description="Working directory")

class CommandResponse(BaseModel):
    exit_code: int
    result: str
    execution_time: float

# Chat-related models
class ChatMessageRequest(BaseModel):
    message: str = Field(..., description="Chat message content")
    message_type: str = Field("user", description="Message type: user, system, error, status")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Message metadata")

class ChatMessageResponse(BaseModel):
    id: str
    sandbox_id: str
    user_id: str
    message_type: str
    content: str
    command_id: Optional[str] = None
    created_at: str
    metadata: Dict[str, Any] = {}

class ChatHistoryResponse(BaseModel):
    messages: List[ChatMessageResponse]
    total: int
    page: int
    page_size: int

# Waitlist-related models
class WaitlistRequest(BaseModel):
    email: EmailStr = Field(..., description="Email address for waitlist")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional metadata")
    website: Optional[str] = Field("", description="Honeypot field - should be empty")

class WaitlistResponse(BaseModel):
    id: str
    email: str
    created_at: str
    message: str

# Utility functions
def validate_github_token(token: str) -> bool:
    """Validate GitHub token format"""
    if not token:
        return False
    
    valid_prefixes = ['ghp_', 'gho_', 'ghu_', 'github_pat_', 'ghs_']
    has_valid_prefix = any(token.startswith(prefix) for prefix in valid_prefixes)
    has_valid_length = len(token) >= 40
    
    return has_valid_prefix and has_valid_length

def validate_github_url(url: str) -> bool:
    """Validate GitHub repository URL format"""
    if not url:
        return False
    
    patterns = [
        r'^https://github\.com/[^/]+/[^/]+\.git$',
        r'^https://github\.com/[^/]+/[^/]+$',
        r'^git@github\.com:[^/]+/[^/]+\.git$'
    ]
    
    import re
    return any(re.match(pattern, url) for pattern in patterns)

def generate_sandbox_name(repo_url: str) -> str:
    """Generate unique sandbox name from repository URL"""
    repo_name = repo_url.split('/')[-1].replace('.git', '')
    timestamp = datetime.now().strftime('%Y%m%d-%H%M%S')
    return f"voicecode-{repo_name}-{timestamp}"


def create_jwt_token(user_data: Dict[str, Any]) -> str:
    """Create JWT token for user"""
    payload = {
        "user": user_data,
        "exp": datetime.now(timezone.utc) + timedelta(hours=24),
        "iat": datetime.now(timezone.utc),
        "iss": "voicecode-api"
    }
    return jwt.encode(payload, settings.jwt_secret, algorithm=settings.jwt_algorithm)

def verify_jwt_token(token: str) -> Dict[str, Any]:
    """Verify JWT token and return user data"""
    try:
        payload = jwt.decode(token, settings.jwt_secret, algorithms=[settings.jwt_algorithm])
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token expired")
    except jwt.InvalidTokenError:
        raise HTTPException(status_code=401, detail="Invalid token")

# GitHub Apps functions removed - using OAuth flow instead

# Dependency to get current user
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get current user from JWT token"""
    token = credentials.credentials
    return verify_jwt_token(token)

# Dependency to get TaskService
def get_task_service(db: Session = Depends(get_db)) -> TaskService:
    """Get TaskService instance with dependencies"""
    return TaskService(
        db_session=db,
        redis_client=redis_client,
        daytona_service=daytona
    )

# GitHub OAuth endpoints
@app.get("/api/auth/github")
async def github_oauth(platform: str = "web"):
    """Initialize GitHub OAuth flow with platform-specific configuration"""
    logger.info(f"🔑 Starting GitHub OAuth flow for platform: {platform}")
    
    # Determine which OAuth client to use based on platform
    if platform == "web":
        client_id = settings.github_oauth_web_client_id
        redirect_uri = f"{settings.app_url}/api/auth/github/callback"
        if not client_id:
            raise HTTPException(status_code=500, detail="GitHub Web OAuth not configured")
    elif platform == "mobile":
        client_id = settings.github_oauth_client_id
        redirect_uri = "voicecode://oauth/callback"
        if not client_id:
            raise HTTPException(status_code=500, detail="GitHub Mobile OAuth not configured")
    else:
        raise HTTPException(status_code=400, detail="Invalid platform. Must be 'web' or 'mobile'")
    
    state = str(uuid.uuid4())
    store_data(f"oauth_state:{state}", platform, 600)  # Store platform with state for 10 minutes
    
    # GitHub OAuth URL with repo scope for cloning
    github_oauth_url = (
        f"https://github.com/login/oauth/authorize"
        f"?client_id={client_id}"
        f"&redirect_uri={redirect_uri}"
        f"&scope=repo,user:email,read:org"
        f"&state={state}"
    )
    
    return {
        "data": {"auth_url": github_oauth_url, "state": state, "type": "github_oauth"},
        "message": "GitHub OAuth URL generated successfully"
    }

@app.get("/api/auth/github/callback")
async def github_oauth_callback(code: Optional[str] = None, state: Optional[str] = None):
    """Handle GitHub OAuth callback"""
    logger.info("🔑 Handling GitHub OAuth callback")
    
    if not code or not state:
        raise HTTPException(status_code=400, detail="Missing code or state parameter")
    
    # Verify state parameter and get platform
    stored_platform = get_data(f"oauth_state:{state}")
    if not stored_platform:
        raise HTTPException(status_code=400, detail="Invalid state parameter")
    
    # Determine which OAuth client to use based on stored platform
    if stored_platform == "web":
        client_id = settings.github_oauth_web_client_id
        client_secret = settings.github_oauth_web_client_secret
    else:  # mobile or legacy
        client_id = settings.github_oauth_client_id
        client_secret = settings.github_oauth_client_secret
    
    try:
        # Exchange code for access token
        async with httpx.AsyncClient() as client:
            token_response = await client.post(
                "https://github.com/login/oauth/access_token",
                data={
                    "client_id": client_id,
                    "client_secret": client_secret,
                    "code": code,
                    "state": state
                },
                headers={"Accept": "application/json"}
            )
            
            if token_response.status_code != 200:
                raise HTTPException(status_code=400, detail="Failed to exchange code for token")
            
            token_data = token_response.json()
            access_token = token_data.get("access_token")
            
            if not access_token:
                raise HTTPException(status_code=400, detail="No access token received")
            
            # Get user info
            user_response = await client.get(
                "https://api.github.com/user",
                headers={"Authorization": f"token {access_token}"}
            )
            
            if user_response.status_code != 200:
                raise HTTPException(status_code=400, detail="Failed to get user info")
            
            user_data = user_response.json()
            
            # Create JWT token with OAuth data
            jwt_token = create_jwt_token({
                "github_token": access_token,
                "user": user_data,
                "auth_type": "oauth"
            })
            
            # Store in storage
            store_data(f"user_token:{user_data['id']}", jwt_token, 86400)
            delete_data(f"oauth_state:{state}")
            
            logger.info(f"✅ GitHub OAuth successful for user {user_data['login']}")
            
            # Store the auth data temporarily in Redis for the frontend to retrieve
            # Use underscore instead of colon to avoid URL encoding issues
            temp_auth_key = f"temp_auth_{str(uuid.uuid4())}"
            auth_data = {
                "access_token": jwt_token,
                "token_type": "bearer", 
                "expires_in": 86400,
                "user": user_data
            }
            store_data(temp_auth_key, json.dumps(auth_data), 300)  # 5 minutes
            logger.info(f"🔑 Stored auth data with key: {temp_auth_key}")
            
            # Debug: Verify the data was stored
            test_retrieve = get_data(temp_auth_key)
            if test_retrieve:
                logger.info(f"✅ Verified auth data storage - key exists: {temp_auth_key}")
            else:
                logger.error(f"❌ Failed to verify auth data storage for key: {temp_auth_key}")
            
            # Redirect to frontend callback with the temporary key
            frontend_callback_url = f"{settings.frontend_url}/auth/callback?auth_key={temp_auth_key}"
            return RedirectResponse(url=frontend_callback_url, status_code=302)
            
    except Exception as e:
        logger.error(f"❌ GitHub OAuth failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"OAuth failed: {str(e)}")


@app.get("/api/auth/token/{auth_key}")
async def get_auth_data(auth_key: str, request: Request):
    """Retrieve auth data using temporary key"""
    logger.info(f"🔍 Looking for auth data with key: {auth_key}")
    logger.info(f"🔍 Request origin: {request.headers.get('origin', 'No origin header')}")
    
    # The key is already the full key (e.g., temp_auth_uuid)
    auth_data = get_data(auth_key)
    
    if not auth_data:
        # Debug: List all keys to see what's available
        all_keys = get_keys("temp_auth*")
        logger.error(f"❌ Auth data not found for key: {auth_key}")
        logger.error(f"Available temp_auth keys: {all_keys}")
        
        raise HTTPException(status_code=404, detail="Auth data not found or expired")
    
    logger.info(f"✅ Found auth data for key: {auth_key}")
    
    # Parse the stored JSON data
    auth_json = json.loads(auth_data)
    
    # Delete the temporary key after use
    delete_data(auth_key)
    
    # Return the data in the expected format
    return {
        "data": auth_json,
        "message": "Auth data retrieved successfully"
    }

@app.post("/api/auth/mobile/exchange")
async def mobile_token_exchange(request: MobileTokenExchangeRequest):
    """Exchange authorization code for tokens with PKCE verification"""
    logger.info("🔑 Mobile token exchange initiated")
    
    try:
        # Exchange code for access token with GitHub using standard OAuth flow
        # Note: GitHub OAuth Apps don't support PKCE, only GitHub Apps do
        async with httpx.AsyncClient() as client:
            token_response = await client.post(
                "https://github.com/login/oauth/access_token",
                data={
                    "client_id": settings.github_oauth_client_id,
                    "client_secret": settings.github_oauth_client_secret,
                    "code": request.authorization_code,
                    "redirect_uri": request.redirect_uri
                },
                headers={"Accept": "application/json"}
            )
            
            if token_response.status_code != 200:
                raise HTTPException(status_code=400, detail="Failed to exchange code for token")
            
            token_data = token_response.json()
            access_token = token_data.get("access_token")
            
            if not access_token:
                raise HTTPException(status_code=400, detail="No access token received")
            
            # Get user info from GitHub
            user_response = await client.get(
                "https://api.github.com/user",
                headers={"Authorization": f"token {access_token}"}
            )
            
            if user_response.status_code != 200:
                raise HTTPException(status_code=400, detail="Failed to get user info")
            
            user_data = user_response.json()
            
            # Create JWT token with OAuth data
            jwt_token = create_jwt_token({
                "github_token": access_token,
                "user": user_data,
                "auth_type": "mobile_oauth"
            })
            
            # Store in storage for session management
            store_data(f"user_token:{user_data['id']}", jwt_token, 86400)
            
            logger.info(f"✅ Mobile OAuth successful for user {user_data['login']}")
            
            # Return auth data in expected format
            auth_data = {
                "access_token": jwt_token,
                "token_type": "bearer", 
                "expires_in": 86400,
                "user": {
                    "id": str(user_data['id']),
                    "username": user_data['login'],
                    "email": user_data.get('email', ''),
                    "avatar_url": user_data['avatar_url'],
                    "github_token": access_token
                }
            }
            
            return {
                "data": auth_data,
                "message": "Mobile authentication successful"
            }
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Mobile token exchange failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Token exchange failed: {str(e)}")

@app.get("/api/auth/me")
async def get_current_user_info(current_user: Dict = Depends(get_current_user)):
    """Get current user information"""
    user_data = current_user["user"]
    return {
        "data": {
            "user": user_data["user"] if "user" in user_data else user_data,
            "auth_type": user_data.get("auth_type", "oauth")
        }
    }

# Repository endpoints
@app.get("/api/repositories")
async def list_repositories(current_user: Dict = Depends(get_current_user)):
    """List accessible repositories via GitHub OAuth"""
    logger.info(f"🔍 Fetching repositories for user: {current_user['user']['user']['login']}")
    
    github_token = current_user["user"].get("github_token")
    if not github_token:
        logger.error("❌ No GitHub token found in user data")
        raise HTTPException(status_code=400, detail="No GitHub token found")
    
    logger.info(f"🔐 Using GitHub token: {github_token[:10]}...{github_token[-4:]}")
    
    try:
        async with httpx.AsyncClient() as client:
            logger.info("🚀 Making GitHub API request to /user/repos")
            response = await client.get(
                "https://api.github.com/user/repos",
                headers={"Authorization": f"token {github_token}"},
                params={"per_page": 100, "sort": "updated", "affiliation": "owner,collaborator"}
            )
            
            if response.status_code != 200:
                logger.error(f"❌ GitHub API error - Status: {response.status_code}, Response: {response.text}")
                raise HTTPException(status_code=400, detail="Failed to fetch repositories")
            
            repos = response.json()
            logger.info(f"✅ Successfully fetched {len(repos)} repositories")
            
            return {
                "data": {
                    "total_count": len(repos),
                    "incomplete_results": False,
                    "items": repos
                },
                "message": "Repositories fetched successfully"
            }
            
    except Exception as e:
        logger.error(f"❌ Failed to fetch repositories: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch repositories: {str(e)}")

@app.get("/api/repositories/{owner}/{repo}/branches")
async def list_branches(owner: str, repo: str, current_user: Dict = Depends(get_current_user)):
    """List repository branches"""
    github_token = current_user["user"].get("github_token")
    if not github_token:
        raise HTTPException(status_code=400, detail="No GitHub token found")
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"https://api.github.com/repos/{owner}/{repo}/branches",
                headers={"Authorization": f"token {github_token}"}
            )
            
            if response.status_code != 200:
                raise HTTPException(status_code=400, detail="Failed to fetch branches")
            
            branches = response.json()
            return {
                "data": {"branches": branches},
                "message": "Branches fetched successfully"
            }
            
    except Exception as e:
        logger.error(f"❌ Failed to fetch branches: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch branches: {str(e)}")

# Sandbox management endpoints
@app.post("/api/sandbox/create")
async def create_sandbox(
    request: SandboxCreateRequest,
    current_user: Dict = Depends(get_current_user)
):
    """Create a new sandbox with repository"""
    logger.info(f"🚀 Creating sandbox for repo: {request.repo_url}")
    
    # Validate inputs
    if not validate_github_url(request.repo_url):
        raise HTTPException(status_code=400, detail="Invalid GitHub repository URL")
    
    # Validate Claude Code OAuth token is provided
    if not request.claude_code_oauth_token or not request.claude_code_oauth_token.strip():
        raise HTTPException(status_code=400, detail="Claude Code OAuth token is required")
    
    github_token = current_user["user"].get("github_token")
    if not github_token:
        raise HTTPException(status_code=400, detail="No GitHub token found")
    
    # Use OAuth token for git operations
    token_to_use = github_token
    
    try:
        # Generate sandbox name
        sandbox_name = request.sandbox_name or generate_sandbox_name(request.repo_url)
        
        # Prepare environment variables - merge from config and add CLAUDE_CODE_OAUTH_TOKEN
        env_vars = {}
        if request.config and request.config.environment_variables:
            env_vars = request.config.environment_variables.copy()
        
        # CLAUDE_CODE_OAUTH_TOKEN from dedicated field takes precedence
        if request.claude_code_oauth_token:
            env_vars["CLAUDE_CODE_OAUTH_TOKEN"] = request.claude_code_oauth_token
        
        # Create sandbox using Daytona SDK with comprehensive configuration
        params_dict = {"snapshot": settings.daytona_image}
        
        # Add environment variables if provided
        if env_vars:
            params_dict["env_vars"] = env_vars
            logger.info(f"🔧 Setting environment variables: {list(env_vars.keys())}")
        
        # Add configuration parameters if provided
        if request.config:
            config = request.config
            
            # Set programming language
            if config.language:
                params_dict["language"] = config.language
                logger.info(f"🎯 Setting language: {config.language}")
            
            # Set auto-stop interval
            if config.auto_stop_interval is not None:
                params_dict["auto_stop_interval"] = config.auto_stop_interval
                logger.info(f"⏱️ Setting auto-stop interval: {config.auto_stop_interval} minutes")
            
            # Set auto-archive interval
            if config.auto_archive_interval is not None:
                params_dict["auto_archive_interval"] = config.auto_archive_interval
                logger.info(f"📦 Setting auto-archive interval: {config.auto_archive_interval} minutes")
            
            # Set custom labels
            if config.labels:
                params_dict["labels"] = config.labels
                logger.info(f"🏷️ Setting labels: {config.labels}")
            
            # Set custom resources (CPU, memory, storage)
            if config.cpu or config.memory or config.storage:
                resources_dict = {}
                if config.cpu:
                    resources_dict["cpu"] = config.cpu
                    logger.info(f"💻 Setting CPU cores: {config.cpu}")
                if config.memory:
                    resources_dict["memory"] = config.memory  # Already in GB
                    logger.info(f"🧠 Setting memory: {config.memory} GB")
                if config.storage:
                    resources_dict["disk"] = config.storage  # Convert to disk parameter
                    logger.info(f"💾 Setting storage: {config.storage} GB")
                
                params_dict["resources"] = Resources(**resources_dict)
        
        params = CreateSandboxFromSnapshotParams(**params_dict)
        sandbox = daytona.create(params)
        
        logger.info(f"📝 Sandbox created with ID: {sandbox.id}")
        if env_vars:
            logger.info(f"✅ Environment variables configured: {list(env_vars.keys())}")
        if request.config:
            logger.info(f"⚙️ Advanced configuration applied successfully")
        
        # Clone repository
        clone_repository_to_sandbox(
            sandbox, 
            request.repo_url, 
            request.branch, 
            token_to_use
        )
        
        # Store sandbox info in Redis
        sandbox_info = {
            "sandbox_id": sandbox.id,
            "sandbox_name": sandbox_name,
            "repo_url": request.repo_url,
            "branch": request.branch,
            "status": "ready",
            "created_at": datetime.now().isoformat(),
            "user_id": current_user["user"]["user"]["id"]
        }
        
        redis_client.setex(
            f"sandbox:{sandbox.id}",
            86400,  # 24 hours
            json.dumps(sandbox_info)
        )
        
        logger.info("✅ Sandbox created successfully!")
        return {
            "data": SandboxResponse(**sandbox_info),
            "message": "Sandbox created successfully"
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to create sandbox: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create sandbox: {str(e)}")

def clone_repository_to_sandbox(sandbox, repo_url: str, branch: str, github_token: str):
    """Clone repository to sandbox"""
    logger.info("🔄 Cloning repository to sandbox")
    
    try:
        if github_token:
            # Use authenticated URL
            authenticated_url = repo_url.replace(
                'https://github.com/', 
                f'https://x-access-token:{github_token}@github.com/'
            )
            
            sandbox.git.clone(
                authenticated_url,
                'workspace/repository',
                branch or 'main'
            )
        else:
            # Public repository
            sandbox.git.clone(
                repo_url,
                'workspace/repository',
                branch or 'main'
            )
        
        logger.info("✅ Repository cloned successfully")
        
    except Exception as e:
        logger.error(f"❌ Failed to clone repository: {str(e)}")
        raise

@app.get("/api/sandbox/list")
async def list_sandboxes(current_user: Dict = Depends(get_current_user)):
    """List user's sandboxes with real-time status from Daytona SDK"""
    user_id = current_user["user"]["user"]["id"]
    
    # Get all sandbox keys for the user
    sandbox_keys = redis_client.keys(f"sandbox:*")
    user_sandboxes = []
    
    for key in sandbox_keys:
        sandbox_data = redis_client.get(key)
        if sandbox_data:
            sandbox_info = json.loads(sandbox_data)
            if sandbox_info.get("user_id") == user_id:
                # Sync status from Daytona SDK
                try:
                    daytona_sandbox = daytona.get(sandbox_info["sandbox_id"])
                    sandbox_info["status"] = daytona_sandbox.state
                    # Update Redis with current status
                    redis_client.setex(
                        key,
                        86400,  # 24 hours
                        json.dumps(sandbox_info)
                    )
                except Exception as e:
                    logger.warning(f"Failed to sync status for sandbox {sandbox_info['sandbox_id']}: {e}")
                    # Keep existing status from Redis
                
                user_sandboxes.append(sandbox_info)
    
    return {
        "data": {"sandboxes": user_sandboxes},
        "message": "Sandboxes retrieved successfully"
    }

@app.get("/api/sandbox/{sandbox_id}")
async def get_sandbox(sandbox_id: str, current_user: Dict = Depends(get_current_user)):
    """Get specific sandbox with real-time status from Daytona SDK"""
    user_id = current_user["user"]["user"]["id"]
    
    # Get sandbox from Redis
    sandbox_data = redis_client.get(f"sandbox:{sandbox_id}")
    if not sandbox_data:
        raise HTTPException(status_code=404, detail="Sandbox not found")
    
    sandbox_info = json.loads(sandbox_data)
    if sandbox_info.get("user_id") != user_id:
        raise HTTPException(status_code=403, detail="Not authorized to access this sandbox")
    
    # Sync status from Daytona SDK
    try:
        daytona_sandbox = daytona.get(sandbox_id)
        sandbox_info["status"] = daytona_sandbox.state
        
        # Update Redis with current status
        redis_client.setex(
            f"sandbox:{sandbox_id}",
            86400,  # 24 hours
            json.dumps(sandbox_info)
        )
        
        logger.info(f"✅ Synced sandbox {sandbox_id} status: {daytona_sandbox.state}")
        
    except Exception as e:
        logger.warning(f"Failed to sync status for sandbox {sandbox_id}: {e}")
        # Keep existing status from Redis
    
    return {
        "data": sandbox_info,
        "message": "Sandbox retrieved successfully"
    }

@app.post("/api/sandbox/{sandbox_id}/start")
async def start_sandbox(sandbox_id: str, current_user: Dict = Depends(get_current_user)):
    """Start a stopped sandbox"""
    logger.info(f"🚀 Starting sandbox: {sandbox_id}")
    
    # Verify ownership
    sandbox_data = redis_client.get(f"sandbox:{sandbox_id}")
    if not sandbox_data:
        raise HTTPException(status_code=404, detail="Sandbox not found")
    
    sandbox_info = json.loads(sandbox_data)
    if sandbox_info.get("user_id") != current_user["user"]["user"]["id"]:
        raise HTTPException(status_code=403, detail="Not authorized to start this sandbox")
    
    try:
        sandbox = daytona.get(sandbox_id)
        
        # Check current status from Daytona SDK
        current_state = sandbox.state
        logger.info(f"Current sandbox state: {current_state}")
        
        # Check if sandbox is already started
        if current_state in ["started", "running"]:
            logger.info("✅ Sandbox is already running")
            # Update Redis with current status
            sandbox_info["status"] = current_state
            redis_client.setex(
                f"sandbox:{sandbox_id}",
                86400,  # 24 hours
                json.dumps(sandbox_info)
            )
            return {
                "data": {"status": current_state},
                "message": "Sandbox is already running"
            }
        
        # Start the sandbox
        sandbox.start(timeout=120)  # 2 minutes timeout
        
        # Get updated status after start
        updated_sandbox = daytona.get(sandbox_id)
        final_state = updated_sandbox.state
        
        # Update status in Redis with actual state
        sandbox_info["status"] = final_state
        redis_client.setex(
            f"sandbox:{sandbox_id}",
            86400,  # 24 hours
            json.dumps(sandbox_info)
        )
        
        logger.info(f"✅ Sandbox start operation completed. Final state: {final_state}")
        return {
            "data": {"status": final_state},
            "message": "Sandbox start operation completed"
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to start sandbox: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to start sandbox: {str(e)}")

@app.post("/api/sandbox/{sandbox_id}/stop")
async def stop_sandbox(sandbox_id: str, current_user: Dict = Depends(get_current_user)):
    """Stop a running sandbox"""
    logger.info(f"🛑 Stopping sandbox: {sandbox_id}")
    
    # Verify ownership
    sandbox_data = redis_client.get(f"sandbox:{sandbox_id}")
    if not sandbox_data:
        raise HTTPException(status_code=404, detail="Sandbox not found")
    
    sandbox_info = json.loads(sandbox_data)
    if sandbox_info.get("user_id") != current_user["user"]["user"]["id"]:
        raise HTTPException(status_code=403, detail="Not authorized to stop this sandbox")
    
    try:
        sandbox = daytona.get(sandbox_id)
        
        # Check current status from Daytona SDK
        current_state = sandbox.state
        logger.info(f"Current sandbox state: {current_state}")
        
        # Check if sandbox is already stopped
        if current_state in ["stopped", "stopping"]:
            logger.info("✅ Sandbox is already stopped")
            # Update Redis with current status
            sandbox_info["status"] = current_state
            redis_client.setex(
                f"sandbox:{sandbox_id}",
                86400,  # 24 hours
                json.dumps(sandbox_info)
            )
            return {
                "data": {"status": current_state},
                "message": "Sandbox is already stopped"
            }
        
        # Stop the sandbox
        sandbox.stop(timeout=120)  # 2 minutes timeout
        
        # Get updated status after stop
        updated_sandbox = daytona.get(sandbox_id)
        final_state = updated_sandbox.state
        
        # Update status in Redis with actual state
        sandbox_info["status"] = final_state
        redis_client.setex(
            f"sandbox:{sandbox_id}",
            86400,  # 24 hours
            json.dumps(sandbox_info)
        )
        
        logger.info(f"✅ Sandbox stop operation completed. Final state: {final_state}")
        return {
            "data": {"status": final_state},
            "message": "Sandbox stop operation completed"
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to stop sandbox: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to stop sandbox: {str(e)}")

@app.delete("/api/sandbox/{sandbox_id}")
async def delete_sandbox(sandbox_id: str, current_user: Dict = Depends(get_current_user)):
    """Delete a sandbox"""
    logger.info(f"🗑️  Deleting sandbox: {sandbox_id}")
    
    # Verify ownership
    sandbox_data = redis_client.get(f"sandbox:{sandbox_id}")
    if not sandbox_data:
        raise HTTPException(status_code=404, detail="Sandbox not found")
    
    sandbox_info = json.loads(sandbox_data)
    if sandbox_info.get("user_id") != current_user["user"]["user"]["id"]:
        raise HTTPException(status_code=403, detail="Not authorized to delete this sandbox")
    
    try:
        sandbox = daytona.get(sandbox_id)
        # Delete from Daytona
        daytona.delete(sandbox)
        
        # Remove from Redis
        redis_client.delete(f"sandbox:{sandbox_id}")
        
        logger.info("✅ Sandbox deleted successfully")
        return {
            "data": {"success": True},
            "message": "Sandbox deleted successfully"
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to delete sandbox: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete sandbox: {str(e)}")

# Claude CLI command execution
@app.post("/api/sandbox/{sandbox_id}/execute")
async def execute_command(
    sandbox_id: str,
    request: CommandRequest,
    current_user: Dict = Depends(get_current_user)
):
    """Execute a command in the sandbox"""
    logger.info(f"🔄 Original command in sandbox {sandbox_id}: {request.command}")
    
    # Process the command before execution
    processed_command = command_processor.process_command(request.command)
    if processed_command != request.command:
        logger.info(f"🔄 Processed command: {processed_command}")
    
    # Verify ownership
    sandbox_data = redis_client.get(f"sandbox:{sandbox_id}")
    if not sandbox_data:
        raise HTTPException(status_code=404, detail="Sandbox not found")
    
    sandbox_info = json.loads(sandbox_data)
    if sandbox_info.get("user_id") != current_user["user"]["user"]["id"]:
        raise HTTPException(status_code=403, detail="Not authorized to access this sandbox")
    
    try:
        # Get sandbox instance
        sandbox = daytona.get(sandbox_id)
        
        # Execute processed command
        start_time = datetime.now()
        response = sandbox.process.exec(
            processed_command,
            request.working_directory
        )
        end_time = datetime.now()
        
        execution_time = (end_time - start_time).total_seconds()
        
        logger.info(f"✅ Command executed successfully (exit_code: {response.exit_code})")
        logger.info(f"Working Directory: {request.working_directory}")
        
        return {
            "data": CommandResponse(
                exit_code=response.exit_code,
                result=response.result,
                execution_time=execution_time
            ),
            "message": "Command executed successfully"
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to execute command: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to execute command: {str(e)}")

# Chat endpoints
@app.post("/api/sandbox/{sandbox_id}/chat")
async def send_chat_message(
    sandbox_id: str,
    request: ChatMessageRequest,
    current_user: Dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Send chat message and optionally execute command"""
    logger.info(f"💬 Sending chat message to sandbox: {sandbox_id}")
    
    user_id = current_user["user"]["user"]["id"]
    
    # Verify sandbox ownership
    sandbox_data = redis_client.get(f"sandbox:{sandbox_id}")
    if not sandbox_data:
        raise HTTPException(status_code=404, detail="Sandbox not found")
    
    sandbox_info = json.loads(sandbox_data)
    if sandbox_info.get("user_id") != user_id:
        raise HTTPException(status_code=403, detail="Not authorized to access this sandbox")
    
    try:
        # Convert user_id to UUID format (GitHub ID is an integer, we need to create a deterministic UUID)
        user_uuid = uuid.uuid5(uuid.NAMESPACE_OID, str(user_id))
        
        # Create chat message in database
        logger.info(f"🔍 Creating chat message with type: {request.message_type}")
        chat_message = ChatMessage(
            sandbox_id=uuid.UUID(sandbox_id),
            user_id=user_uuid,
            message_type=request.message_type,
            content=request.message,
            message_metadata=request.metadata or {}
        )
        

        
        db.add(chat_message)
        db.commit()
        db.refresh(chat_message)
        
        logger.info(f"✅ Chat message saved with ID: {chat_message.id}")
        
        # Don't execute commands in the chat endpoint - let frontend handle it through /execute endpoint
        
        return {
            "data": {
                "message_id": str(chat_message.id),
                "status": "sent"
            },
            "message": "Chat message sent successfully"
        }
        
    except Exception as e:
        db.rollback()
        logger.error(f"❌ Failed to send chat message: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to send chat message: {str(e)}")

@app.get("/api/sandbox/{sandbox_id}/chat/history")
async def get_chat_history(
    sandbox_id: str,
    page: int = 1,
    page_size: int = 50,
    message_type: Optional[str] = None,
    current_user: Dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get chat history for sandbox with pagination"""
    logger.info(f"📜 Getting chat history for sandbox: {sandbox_id}")
    
    user_id = current_user["user"]["user"]["id"]
    
    # Verify sandbox ownership
    sandbox_data = redis_client.get(f"sandbox:{sandbox_id}")
    if not sandbox_data:
        raise HTTPException(status_code=404, detail="Sandbox not found")
    
    sandbox_info = json.loads(sandbox_data)
    if sandbox_info.get("user_id") != user_id:
        raise HTTPException(status_code=403, detail="Not authorized to access this sandbox")
    
    try:
        # Convert user_id to UUID format (GitHub ID is an integer, we need to create a deterministic UUID)
        # Use UUID v5 with a namespace to create consistent UUIDs from GitHub user IDs
        user_uuid = uuid.uuid5(uuid.NAMESPACE_OID, str(user_id))
        
        # Build query
        query = db.query(ChatMessage).filter(
            ChatMessage.sandbox_id == uuid.UUID(sandbox_id),
            ChatMessage.user_id == user_uuid
        )
        
        # Filter by message type if specified
        if message_type:
            query = query.filter(ChatMessage.message_type == message_type)
        
        # Get total count
        total = query.count()
        
        # Apply pagination and ordering
        messages = query.order_by(ChatMessage.created_at.asc()).offset(
            (page - 1) * page_size
        ).limit(page_size).all()
        
        # Convert to response format
        message_responses = [
            ChatMessageResponse(**msg.to_dict()) for msg in messages
        ]
        
        
        logger.info(f"✅ Retrieved {len(message_responses)} messages (page {page})")
        
        return {
            "data": ChatHistoryResponse(
                messages=message_responses,
                total=total,
                page=page,
                page_size=page_size
            ),
            "message": "Chat history retrieved successfully"
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get chat history: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get chat history: {str(e)}")

@app.delete("/api/sandbox/{sandbox_id}/chat")
async def clear_chat_history(
    sandbox_id: str,
    current_user: Dict = Depends(get_current_user),
    db: Session = Depends(get_db),
    task_service: TaskService = Depends(dependencies.get_task_service)
):
    """Clear chat history for sandbox"""
    logger.info(f"🗑️ Clearing chat history for sandbox: {sandbox_id}")
    
    user_id = current_user["user"]["user"]["id"]
    
    # Verify sandbox ownership
    sandbox_data = redis_client.get(f"sandbox:{sandbox_id}")
    if not sandbox_data:
        raise HTTPException(status_code=404, detail="Sandbox not found")
    
    sandbox_info = json.loads(sandbox_data)
    if sandbox_info.get("user_id") != user_id:
        raise HTTPException(status_code=403, detail="Not authorized to access this sandbox")
    
    try:
        # Convert user_id to UUID format (GitHub ID is an integer, we need to create a deterministic UUID)
        user_uuid = uuid.uuid5(uuid.NAMESPACE_OID, str(user_id))
        sandbox_uuid = uuid.UUID(sandbox_id)
        
        # Cancel any active tasks for this sandbox/user before clearing chat
        active_task = await task_service.get_current_task(sandbox_uuid, user_uuid)
        cancelled_tasks = 0
        if active_task:
            success = await task_service.cancel_task(active_task.id, user_uuid)
            if success:
                cancelled_tasks = 1
                logger.info(f"✅ Cancelled active task {active_task.id}")
            else:
                logger.warning(f"⚠️ Failed to cancel task {active_task.id}")

        # Delete all messages for this sandbox and user
        deleted_count = db.query(ChatMessage).filter(
            ChatMessage.sandbox_id == sandbox_uuid,
            ChatMessage.user_id == user_uuid
        ).delete()

        # Delete all tasks for this sandbox (both active and completed)
        deleted_tasks_count = db.query(Task).filter(
            Task.sandbox_id == sandbox_uuid
        ).delete()

        db.commit()

        logger.info(f"✅ Cleared {deleted_count} chat messages, {cancelled_tasks} active tasks, and {deleted_tasks_count} task records")

        return {
            "data": {
                "deleted_count": deleted_count,
                "cancelled_tasks": cancelled_tasks,
                "deleted_tasks": deleted_tasks_count
            },
            "message": "Chat history, active tasks, and task records cleared successfully"
        }
        
    except Exception as e:
        db.rollback()
        logger.error(f"❌ Failed to clear chat history: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to clear chat history: {str(e)}")

# Waitlist endpoints
@app.post("/api/waitlist")
@limiter.limit("3/hour")
async def join_waitlist(
    request: Request,
    waitlist_request: WaitlistRequest,
    db: Session = Depends(get_db)
):
    """Join the waitlist with email address"""
    logger.info(f"📧 Waitlist signup request for: {waitlist_request.email}")
    
    try:
        # Get client IP for logging
        client_ip = "unknown"
        if request and request.client:
            client_ip = request.client.host
        
        # Check honeypot field
        if waitlist_request.website and waitlist_request.website.strip():
            logger.warning(f"🍯 Honeypot triggered for email: {waitlist_request.email}, IP: {client_ip}")
            # Silently reject but return success to avoid revealing the honeypot
            return {
                "data": WaitlistResponse(
                    id="00000000-0000-0000-0000-000000000000",
                    email=waitlist_request.email,
                    created_at=datetime.now().isoformat(),
                    message="Welcome to the waitlist!"
                ),
                "message": "Successfully joined waitlist"
            }
        
        # Validate email security
        is_valid_email, email_error = validate_email_security(waitlist_request.email)
        if not is_valid_email:
            logger.warning(f"🚫 Invalid email rejected: {waitlist_request.email}, reason: {email_error}")
            raise HTTPException(status_code=400, detail=email_error)
        
        # Check if email already exists
        existing_entry = db.query(WaitlistEntry).filter(
            WaitlistEntry.email == waitlist_request.email
        ).first()
        
        if existing_entry:
            logger.info(f"📧 Email {waitlist_request.email} already exists in waitlist")
            return {
                "data": WaitlistResponse(
                    id=str(existing_entry.id),
                    email=existing_entry.email,
                    created_at=existing_entry.created_at.isoformat(),
                    message="You're already on the waitlist!"
                ),
                "message": "Email already registered"
            }
        
        # Collect metadata from waitlist_request
        metadata = waitlist_request.metadata.copy() if waitlist_request.metadata else {}
        if request:
            metadata.update({
                "user_agent": request.headers.get("user-agent", ""),
                "referer": request.headers.get("referer", ""),
                "origin": request.headers.get("origin", ""),
                "ip_address": client_ip,
            })
        
        # Determine if submission is suspicious
        is_suspicious = False
        suspicious_reasons = []
        
        # Check for suspicious patterns in metadata
        user_agent = metadata.get("user_agent", "").lower()
        if not user_agent or "bot" in user_agent or "crawler" in user_agent:
            is_suspicious = True
            suspicious_reasons.append("suspicious_user_agent")
        
        # Check for disposable email (already checked above, but log it)
        if is_disposable_email(waitlist_request.email):
            is_suspicious = True
            suspicious_reasons.append("disposable_email")
        
        # Add suspicious reasons to metadata
        if suspicious_reasons:
            metadata["suspicious_reasons"] = suspicious_reasons
        
        # Create new waitlist entry
        waitlist_entry = WaitlistEntry(
            email=waitlist_request.email,
            entry_metadata=metadata,
            is_suspicious=is_suspicious
        )
        
        db.add(waitlist_entry)
        db.commit()
        db.refresh(waitlist_entry)
        
        if is_suspicious:
            logger.warning(f"⚠️ Suspicious entry added: {waitlist_request.email} - reasons: {suspicious_reasons}")
        else:
            logger.info(f"✅ Successfully added {waitlist_request.email} to waitlist with ID: {waitlist_entry.id}")
        
        return {
            "data": WaitlistResponse(
                id=str(waitlist_entry.id),
                email=waitlist_entry.email,
                created_at=waitlist_entry.created_at.isoformat(),
                message="Welcome to the waitlist!"
            ),
            "message": "Successfully joined waitlist"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"❌ Failed to add to waitlist: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to join waitlist: {str(e)}")

@app.get("/api/waitlist/stats")
async def get_waitlist_stats(
    current_user: Dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get waitlist statistics (admin only)"""
    logger.info("📊 Getting waitlist statistics")
    
    try:
        # Count total entries
        total_entries = db.query(WaitlistEntry).count()
        
        # Count suspicious entries
        suspicious_entries = db.query(WaitlistEntry).filter(
            WaitlistEntry.is_suspicious == True
        ).count()
        
        # Count verified entries
        verified_entries = db.query(WaitlistEntry).filter(
            WaitlistEntry.is_verified == True
        ).count()
        
        # Count entries from last 24 hours
        from datetime import timedelta
        yesterday = datetime.now(timezone.utc) - timedelta(days=1)
        recent_entries = db.query(WaitlistEntry).filter(
            WaitlistEntry.created_at >= yesterday
        ).count()
        
        recent_suspicious = db.query(WaitlistEntry).filter(
            WaitlistEntry.created_at >= yesterday,
            WaitlistEntry.is_suspicious == True
        ).count()
        
        return {
            "data": {
                "total_entries": total_entries,
                "verified_entries": verified_entries,
                "suspicious_entries": suspicious_entries,
                "entries_last_24h": recent_entries,
                "suspicious_last_24h": recent_suspicious,
                "timestamp": datetime.now().isoformat()
            },
            "message": "Waitlist statistics retrieved successfully"
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get waitlist stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get waitlist stats: {str(e)}")

@app.get("/api/waitlist/suspicious")
async def get_suspicious_entries(
    current_user: Dict = Depends(get_current_user),
    db: Session = Depends(get_db),
    page: int = 1,
    page_size: int = 50
):
    """Get suspicious waitlist entries (admin only)"""
    logger.info("🚨 Getting suspicious waitlist entries")
    
    try:
        # Get suspicious entries with pagination
        query = db.query(WaitlistEntry).filter(WaitlistEntry.is_suspicious == True)
        total = query.count()
        
        entries = query.order_by(WaitlistEntry.created_at.desc()).offset(
            (page - 1) * page_size
        ).limit(page_size).all()
        
        return {
            "data": {
                "entries": [entry.to_dict() for entry in entries],
                "total": total,
                "page": page,
                "page_size": page_size
            },
            "message": "Suspicious entries retrieved successfully"
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get suspicious entries: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get suspicious entries: {str(e)}")

# Health check
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

# Documentation endpoint
@app.get("/api/docs")
async def api_documentation():
    """API documentation and testing guide"""
    return {
        "title": "VoiceCode API Documentation",
        "version": "1.0.0",
        "description": "Unified API for GitHub authentication and Daytona sandbox management",
        "base_url": settings.app_url,
        "interactive_docs": f"{settings.app_url}/docs",
        "redoc_docs": f"{settings.app_url}/redoc",
        "endpoints": {
            "authentication": {
                "github_install": {
                    "method": "GET",
                    "url": "/api/auth/github/install",
                    "description": "Initialize GitHub App installation flow"
                },
                "github_callback": {
                    "method": "GET", 
                    "url": "/api/auth/github/callback",
                    "description": "Handle GitHub App installation callback"
                },
                "get_user": {
                    "method": "GET",
                    "url": "/api/auth/me",
                    "description": "Get current user info",
                    "auth_required": True
                }
            },
            "repositories": {
                "list_repos": {
                    "method": "GET",
                    "url": "/api/repositories",
                    "description": "List accessible repositories",
                    "auth_required": True
                },
                "list_branches": {
                    "method": "GET",
                    "url": "/api/repositories/{owner}/{repo}/branches",
                    "description": "List repository branches",
                    "auth_required": True
                }
            },
            "sandbox": {
                "create": {
                    "method": "POST",
                    "url": "/api/sandbox/create",
                    "description": "Create sandbox with repository",
                    "auth_required": True
                },
                "list": {
                    "method": "GET",
                    "url": "/api/sandbox/list",
                    "description": "List user's sandboxes",
                    "auth_required": True
                },
                "start": {
                    "method": "POST",
                    "url": "/api/sandbox/{sandbox_id}/start",
                    "description": "Start a stopped sandbox",
                    "auth_required": True
                },
                "delete": {
                    "method": "DELETE",
                    "url": "/api/sandbox/{sandbox_id}",
                    "description": "Delete sandbox",
                    "auth_required": True
                },
                "execute": {
                    "method": "POST",
                    "url": "/api/sandbox/{sandbox_id}/execute",
                    "description": "Execute command in sandbox",
                    "auth_required": True
                }
            },
            "chat": {
                "send_message": {
                    "method": "POST",
                    "url": "/api/sandbox/{sandbox_id}/chat",
                    "description": "Send chat message and optionally execute command",
                    "auth_required": True
                },
                "get_history": {
                    "method": "GET",
                    "url": "/api/sandbox/{sandbox_id}/chat/history",
                    "description": "Get chat history with pagination",
                    "auth_required": True
                },
                "clear_history": {
                    "method": "DELETE",
                    "url": "/api/sandbox/{sandbox_id}/chat",
                    "description": "Clear chat history for sandbox",
                    "auth_required": True
                }
            },
            "streaming": {
                "stream_task": {
                    "method": "POST",
                    "url": "/api/sandbox/{sandbox_id}/tasks/stream",
                    "description": "Execute command with real-time log streaming via Server-Sent Events",
                    "auth_required": True,
                    "content_type": "text/event-stream",
                    "features": [
                        "Real-time command execution streaming",
                        "Vercel AI SDK compatible SSE format",
                        "Proper task lifecycle management",
                        "Rate limiting protection"
                    ]
                },
                "get_current_task": {
                    "method": "GET", 
                    "url": "/api/sandbox/{sandbox_id}/tasks/current",
                    "description": "Get current task status for sandbox",
                    "auth_required": True
                },
                "cancel_task": {
                    "method": "DELETE",
                    "url": "/api/sandbox/{sandbox_id}/tasks/{task_id}",
                    "description": "Cancel running task",
                    "auth_required": True
                }
            }
        },
        "testing_guide": {
            "steps": [
                "1. Start GitHub App installation: GET /api/auth/github/install",
                "2. Complete GitHub App installation via returned install_url",
                "3. Use returned JWT token in Authorization header",
                "4. Test endpoints with Bearer token authentication"
            ],
            "example_auth_header": "Authorization: Bearer <your-jwt-token>",
            "sample_requests": {
                "create_sandbox": {
                    "url": "/api/sandbox/create",
                    "method": "POST",
                    "headers": {"Authorization": "Bearer <token>"},
                    "body": {
                        "repo_url": "https://github.com/user/repo",
                        "branch": "main"
                    }
                },
                "stream_task": {
                    "url": "/api/sandbox/{sandbox_id}/tasks/stream",
                    "method": "POST",
                    "headers": {
                        "Authorization": "Bearer <token>",
                        "Content-Type": "application/json",
                        "Accept": "text/event-stream"
                    },
                    "body": {
                        "command": "npm test",
                        "metadata": {
                            "source": "chat",
                            "context": {"message_id": "msg-456"}
                        }
                    },
                    "response_format": {
                        "content_type": "text/event-stream",
                        "example_messages": [
                            "data: {\"id\":\"msg-1\",\"role\":\"assistant\",\"content\":\"Executing: npm test\",\"metadata\":{\"type\":\"task_start\",\"task_id\":\"task-123\"}}",
                            "data: {\"id\":\"msg-2\",\"role\":\"assistant\",\"content\":\"Running tests...\",\"metadata\":{\"type\":\"task_log\",\"task_id\":\"task-123\"}}",
                            "data: {\"id\":\"msg-3\",\"role\":\"assistant\",\"content\":\"Task completed (exit code: 0)\",\"metadata\":{\"type\":\"task_complete\",\"task_id\":\"task-123\",\"exit_code\":0}}",
                            "data: [DONE]"
                        ]
                    }
                }
            }
        }
    }

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "VoiceCode API",
        "version": "1.0.0",
        "docs": "/docs",
        "api_docs": "/api/docs"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=9100)
