"""Test script to verify chat endpoints functionality"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from main import app
from database import ChatMessage
import uuid
from datetime import datetime, timezone

def test_chat_models():
    """Test that chat models can be instantiated correctly"""
    # Test ChatMessage model
    test_message = ChatMessage(
        sandbox_id=uuid.uuid4(),
        user_id=uuid.uuid4(),
        message_type="user",
        content="Hello, world!",
        message_metadata={"test": True},
        created_at=datetime.now(timezone.utc)
    )
    
    # Test to_dict method
    message_dict = test_message.to_dict()
    expected_keys = ["id", "sandbox_id", "user_id", "message_type", "content", "command_id", "created_at", "metadata"]
    
    for key in expected_keys:
        if key not in message_dict:
            raise AssertionError(f"Missing key: {key}")

def test_endpoint_definitions():
    """Test that endpoints are properly defined"""
    # Get OpenAPI schema
    schema = app.openapi()
    paths = schema.get("paths", {})
    
    # Check required endpoints
    required_endpoints = [
        "/api/sandbox/{sandbox_id}/chat",
        "/api/sandbox/{sandbox_id}/chat/history"
    ]
    
    for endpoint in required_endpoints:
        if endpoint not in paths:
            raise AssertionError(f"Missing endpoint: {endpoint}")
    
    # Check POST method for chat endpoint
    chat_endpoint = paths["/api/sandbox/{sandbox_id}/chat"]
    if "post" not in chat_endpoint:
        raise AssertionError("Missing POST method for chat endpoint")
    if "delete" not in chat_endpoint:
        raise AssertionError("Missing DELETE method for chat endpoint")
    
    # Check GET method for history endpoint
    history_endpoint = paths["/api/sandbox/{sandbox_id}/chat/history"]
    if "get" not in history_endpoint:
        raise AssertionError("Missing GET method for history endpoint")

def test_pydantic_models():
    """Test Pydantic models for requests and responses"""
    from main import ChatMessageRequest, ChatMessageResponse, ChatHistoryResponse
    
    # Test ChatMessageRequest
    request = ChatMessageRequest(message="Test message", message_type="user")
    
    # Test ChatMessageResponse
    response = ChatMessageResponse(
        id=str(uuid.uuid4()),
        sandbox_id=str(uuid.uuid4()),
        user_id=str(uuid.uuid4()),
        message_type="user",
        content="Test message",
        created_at=datetime.now(timezone.utc).isoformat(),
        metadata={}
    )
    
    # Test ChatHistoryResponse
    history = ChatHistoryResponse(
        messages=[response],
        total=1,
        page=1,
        page_size=50
    )

if __name__ == "__main__":
    try:
        test_chat_models()
        test_endpoint_definitions()
        test_pydantic_models()
    except Exception as e:
        sys.exit(1)