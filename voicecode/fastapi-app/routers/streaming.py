"""Streaming API endpoints for real-time task execution"""

import asyncio
import logging
from uuid import UUID, uuid4
from typing import Dict, AsyncGenerator
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import StreamingResponse
from fastapi.security import H<PERSON><PERSON>uthorizationCredentials, HTT<PERSON><PERSON>earer
from sqlalchemy.orm import Session

from database import get_db
from services import TaskService, CommandProcessor
from services.daytona_service import DaytonaService
from models.streaming_models import TaskExecutionRequest, SSEMessage
from utils.sse_formatter import SSEFormatter, create_sse_generator
from dependencies import (
    get_current_user,
    get_task_service, 
    get_redis_client,
    get_daytona_service,
    get_limiter
)

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/sandbox", tags=["streaming"])
security = HTTPBearer()


def get_command_processor() -> CommandProcessor:
    """Get CommandProcessor instance"""
    return CommandProcessor()


@router.post("/{sandbox_id}/tasks/stream")
async def stream_task_execution(
    request: Request,
    sandbox_id: str,
    task_request: TaskExecutionRequest,
    current_user: Dict = Depends(get_current_user),
    task_service: TaskService = Depends(get_task_service),
    command_processor: CommandProcessor = Depends(get_command_processor),
    db: Session = Depends(get_db)
):
    """
    Execute command with real-time log streaming via Server-Sent Events.
    
    This endpoint executes a command in the specified sandbox and streams
    the execution logs in real-time using SSE format compatible with Vercel AI SDK.
    
    **Features:**
    - Real-time command execution streaming
    - Vercel AI SDK compatible SSE format
    - Proper task lifecycle management
    - Authentication and authorization
    - Rate limiting protection
    
    **SSE Message Format:**
    ```json
    {
        "id": "msg-1",
        "role": "assistant", 
        "content": "Command output line",
        "metadata": {
            "type": "task_log",
            "task_id": "task-uuid",
            "timestamp": "2025-01-31T10:00:01Z"
        }
    }
    ```
    
    **Message Types:**
    - `task_start`: Command execution started
    - `task_log`: Real-time log output
    - `task_complete`: Command completed successfully
    - `task_error`: Error during execution
    
    **Stream Termination:**
    Stream ends with `data: [DONE]` message.
    """
    logger.info(f"🚀 Starting streaming task execution in sandbox {sandbox_id}")
    logger.info(f"📝 Original command: {task_request.command}")
    
    # Process the command before execution
    processed_command = command_processor.process_command(task_request.command)
    if processed_command != task_request.command:
        logger.info(f"🔄 Processed command: {processed_command}")
    
    try:
        # Parse sandbox_id as UUID
        try:
            sandbox_uuid = UUID(sandbox_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid sandbox ID format")
        
        # Extract user information
        user_data = current_user["user"]
        user_id = user_data.get("user", {}).get("id") or user_data.get("id")
        
        # Convert user_id to UUID (GitHub ID is integer, create deterministic UUID)
        import uuid
        user_uuid = uuid.uuid5(uuid.NAMESPACE_OID, str(user_id))
        
        # Verify sandbox access (simplified - check via Redis)
        import json
        redis_client = get_redis_client()
        sandbox_data = None
        if redis_client:
            sandbox_data = redis_client.get(f"sandbox:{sandbox_id}")
        
        if not sandbox_data:
            raise HTTPException(status_code=404, detail="Sandbox not found")
        
        sandbox_info = json.loads(sandbox_data)
        if sandbox_info.get("user_id") != user_id:
            raise HTTPException(
                status_code=403, 
                detail="Not authorized to access this sandbox"
            )
        
        # Check for existing active task
        existing_task = await task_service.get_current_task(sandbox_uuid, user_uuid)
        if existing_task:
            raise HTTPException(
                status_code=409,
                detail=f"Task already running in sandbox: {existing_task.id}"
            )
        
        # Create new task with processed command
        task = await task_service.create_task(
            sandbox_id=sandbox_uuid,
            user_id=user_uuid,
            command=processed_command,
            metadata=task_request.metadata
        )
        
        logger.info(f"✅ Created task {task.id} for streaming execution")
        
        # Create streaming response
        return StreamingResponse(
            stream_task_logs(
                task=task,
                task_service=task_service,
                daytona_service=get_daytona_service(),
                command=processed_command,
                working_directory=task_request.working_directory
            ),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive", 
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
                "Access-Control-Allow-Methods": "*"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to start streaming task: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"Failed to start streaming task: {str(e)}"
        )


async def stream_task_logs(
    task,
    task_service: TaskService,
    daytona_service,
    command: str,
    working_directory: str
) -> AsyncGenerator[str, None]:
    """
    Stream task execution logs as Server-Sent Events.
    
    Args:
        task: Task object from database
        task_service: TaskService instance
        daytona_service: DaytonaService instance  
        command: Command to execute
        working_directory: Working directory for execution
        
    Yields:
        SSE formatted message strings
    """
    formatter = SSEFormatter()
    task_id = str(task.id)
    
    try:
        # Mark task as started
        await task_service.start_task(task.id)
        
        # Send task start message
        yield formatter.format_task_start(command, task_id)
        
        # Use the new session-based streaming implementation
        start_time = datetime.now()
        execution_time = 0
        exit_code = 0
        
        try:
            # Stream command execution in real-time using DaytonaService async streaming
            async for log_entry in daytona_service.execute_command_stream(
                str(task.sandbox_id), 
                command, 
                working_directory
            ):
                # Convert LogEntry to SSE format and yield
                if log_entry.log_type.value == "start":
                    # Task already started, just yield the log content
                    yield formatter.format_task_log(log_entry.content, task_id)
                elif log_entry.log_type.value == "stdout":
                    # Stream real-time log output
                    yield formatter.format_task_log(log_entry.content, task_id)
                elif log_entry.log_type.value == "error":
                    # Handle error logs
                    yield formatter.format_task_log(f"ERROR: {log_entry.content}", task_id)
                    exit_code = 1  # Set error exit code
                elif log_entry.log_type.value == "end":
                    # Command completed successfully
                    exit_code = log_entry.metadata.get("exit_code", 0)
                    break
            
            # Calculate execution time
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            # Complete the task
            await task_service.complete_task(
                task.id, 
                exit_code,
                final_logs=[f"Command execution completed in {execution_time:.2f}s"]
            )
            
            # Send completion message
            yield formatter.format_task_complete(
                task_id, 
                exit_code, 
                execution_time
            )
            
        except Exception as cmd_error:
            logger.error(f"❌ Command execution failed: {cmd_error}")
            
            # Mark task as failed
            await task_service.complete_task(
                task.id,
                1,  # Error exit code
                final_logs=[f"Command execution failed: {str(cmd_error)}"]
            )
            
            # Send error message
            yield formatter.format_task_error(str(cmd_error), task_id)
        
        # Send stream termination
        yield formatter.format_done_message()
        
    except Exception as e:
        logger.error(f"❌ Task streaming failed: {str(e)}")
        
        try:
            # Try to mark task as failed
            await task_service.complete_task(
                task.id,
                1,
                final_logs=[f"Task streaming failed: {str(e)}"]
            )
        except:
            pass  # Best effort cleanup
        
        # Send error and terminate stream
        yield formatter.format_task_error(str(e), task_id)
        yield formatter.format_done_message()


@router.get("/{sandbox_id}/tasks/current")
async def get_current_task_status(
    sandbox_id: str,
    current_user: Dict = Depends(get_current_user),
    task_service: TaskService = Depends(get_task_service)
):
    """Get current task status for sandbox"""
    try:
        # Parse sandbox_id as UUID
        try:
            sandbox_uuid = UUID(sandbox_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid sandbox ID format")
        
        # Get user UUID
        user_data = current_user["user"]
        user_id = user_data.get("user", {}).get("id") or user_data.get("id")
        import uuid
        user_uuid = uuid.uuid5(uuid.NAMESPACE_OID, str(user_id))
        
        # Get current task
        task = await task_service.get_current_task(sandbox_uuid, user_uuid)
        
        if not task:
            return {"data": {"task": None}, "message": "No active task"}
        
        return {
            "data": {
                "task": {
                    "id": str(task.id),
                    "command": task.command,
                    "status": task.status.value,
                    "created_at": task.created_at.isoformat(),
                    "started_at": task.started_at.isoformat() if task.started_at else None,
                    "metadata": task.task_metadata
                }
            },
            "message": "Current task retrieved successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get current task: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get current task: {str(e)}"
        )


@router.delete("/{sandbox_id}/tasks/{task_id}")
async def cancel_task(
    sandbox_id: str,
    task_id: str, 
    current_user: Dict = Depends(get_current_user),
    task_service: TaskService = Depends(get_task_service)
):
    """Cancel running task"""
    try:
        # Parse task_id as UUID
        try:
            task_uuid = UUID(task_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid task ID format")
        
        # Get user UUID
        user_data = current_user["user"]
        user_id = user_data.get("user", {}).get("id") or user_data.get("id")
        import uuid
        user_uuid = uuid.uuid5(uuid.NAMESPACE_OID, str(user_id))
        
        # Cancel task
        success = await task_service.cancel_task(task_uuid, user_uuid)
        
        if success:
            return {
                "data": {"cancelled": True},
                "message": "Task cancelled successfully"
            }
        else:
            return {
                "data": {"cancelled": False},
                "message": "Task could not be cancelled (may already be completed)"
            }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to cancel task: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to cancel task: {str(e)}"
        )