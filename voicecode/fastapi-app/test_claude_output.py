#!/usr/bin/env python3
"""
Test script to understand Claude CLI output behavior
"""

import asyncio
import os
import logging
from datetime import datetime
from daytona import Daytona, DaytonaConfig, SessionExecuteRequest

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_different_commands():
    """Test different command formats to see which produces streamable output"""
    
    # Get credentials from environment
    api_key = os.getenv('DAYTONA_API_KEY')
    api_url = os.getenv('DAYTONA_API_URL', 'https://api.daytona.io')
    target = os.getenv('DAYTONA_TARGET', 'us')
    
    if not api_key:
        logger.error("DAYTONA_API_KEY environment variable not set!")
        return
    
    try:
        # Initialize Daytona client
        config = DaytonaConfig(
            api_key=api_key,
            api_url=api_url,
            target=target
        )
        daytona = Daytona(config)
        
        sandbox_id = "559a67f7-816b-4fec-b2e4-cdb0c02a8153"
        sandbox = daytona.get(sandbox_id)
        
        # Test different command variations
        test_commands = [
            # Basic echo test
            'echo "Testing basic output" && sleep 1 && echo "Done"',
            
            # Direct claude command
            'claude -p "Say hello world"',
            
            # Claude with text output
            'claude -p "Say hello world" --output-format text',
            
            # Claude with unbuffered output
            'stdbuf -oL -eL claude -p "Say hello world" --output-format text',
            
            # Claude with redirection
            'claude -p "Say hello world" --output-format text 2>&1',
            
            # Force TTY behavior
            'script -qec "claude -p \\"Say hello world\\" --output-format text" /dev/null',
        ]
        
        for i, command in enumerate(test_commands):
            logger.info(f"\n{'='*60}")
            logger.info(f"TEST {i+1}: {command}")
            logger.info(f"{'='*60}")
            
            try:
                session_id = f"test-{i+1}-{datetime.now().strftime('%H%M%S')}"
                sandbox.process.create_session(session_id)
                
                cmd_result = sandbox.process.execute_session_command(
                    session_id,
                    SessionExecuteRequest(
                        command=command,
                        run_async=True
                    )
                )
                
                logger.info(f"✅ Command started with ID: {cmd_result.cmd_id}")
                
                # Set up callback
                chunks_received = []
                
                def log_callback(chunk: str):
                    chunks_received.append(chunk)
                    logger.info(f"📝 Chunk: '{chunk.strip()}'")
                
                # Stream with short timeout
                try:
                    await asyncio.wait_for(
                        sandbox.process.get_session_command_logs_async(
                            session_id, 
                            cmd_result.cmd_id, 
                            log_callback
                        ),
                        timeout=30.0
                    )
                    
                    logger.info(f"✅ SUCCESS: Received {len(chunks_received)} chunks")
                    
                except asyncio.TimeoutError:
                    logger.warning(f"⏰ TIMEOUT: Received {len(chunks_received)} chunks before timeout")
                    
                except Exception as e:
                    logger.error(f"❌ ERROR: {e} (Received {len(chunks_received)} chunks)")
                    
            except Exception as e:
                logger.error(f"❌ Failed to test command: {e}")
                
            # Brief pause between tests
            await asyncio.sleep(2)
                
    except Exception as e:
        logger.error(f"❌ Test setup failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_different_commands())