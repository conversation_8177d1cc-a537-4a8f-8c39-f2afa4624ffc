"""Command processing service for VoiceCode CLI integration"""

import logging
from typing import Optional

logger = logging.getLogger(__name__)


class CommandProcessor:
    """Service for processing and sanitizing commands before execution"""
    
    def __init__(self):
        # Define common shell commands that should execute directly
        self.common_commands = {
            # File system operations
            'ls', 'dir', 'pwd', 'cd', 'mkdir', 'rmdir', 'rm', 'mv', 'cp', 'chmod', 'chown',
            # File viewing/editing
            'cat', 'less', 'more', 'head', 'tail', 'grep', 'find', 'locate', 'which', 'whereis',
            # Process management
            'ps', 'top', 'htop', 'kill', 'killall', 'jobs', 'bg', 'fg', 'nohup',
            # Network
            'ping', 'curl', 'wget', 'ssh', 'scp', 'rsync',
            # System info
            'whoami', 'id', 'uname', 'uptime', 'df', 'du', 'free', 'date',
            # Git commands
            'git',
            # Package managers
            'npm', 'pnpm', 'yarn', 'pip', 'apt', 'yum', 'brew',
            # Development tools
            'node', 'python', 'java', 'gcc', 'make', 'docker', 'kubectl'
        }
        
        # Define thresholds for considering a command "long" or natural language
        self.WORD_COUNT_THRESHOLD = 4  # More than 4 words
        self.CHAR_COUNT_THRESHOLD = 30  # More than 30 characters
    
    def process_command(self, command: str) -> str:
        """
        Process command to determine if it should be wrapped with Claude Code
        
        Args:
            command: The original command string
            
        Returns:
            str: The processed command (either original or wrapped with claude -p)
        """
        # Trim and normalize the command
        trimmed_command = command.strip()
        
        if not trimmed_command:
            return trimmed_command
        
        # Check if the command starts with a common command
        first_word = trimmed_command.split()[0].lower() if trimmed_command.split() else ""
        is_common_command = first_word in self.common_commands
        
        word_count = len(trimmed_command.split())
        char_count = len(trimmed_command)
        
        # Determine if this should be wrapped with Claude Code
        # If it's not a common command AND it's long enough, wrap it
        is_long_command = word_count > self.WORD_COUNT_THRESHOLD or char_count > self.CHAR_COUNT_THRESHOLD
        should_wrap_with_claude = not is_common_command and is_long_command
        
        if should_wrap_with_claude:
            logger.info(f"🤖 Wrapping command with Claude Code ({word_count} words, {char_count} chars)")
            # Escape any existing quotes in the command
            escaped_command = trimmed_command.replace('"', '\\"')
            # Force unbuffered output and combine stdout/stderr for streaming
            return f'stdbuf -oL -eL claude -p "{escaped_command}" --output-format text 2>&1'
        
        # Log the actual reason for direct execution
        if is_common_command:
            logger.info(f"⚡ Executing as direct command ({first_word} is a common shell command)")
        else:
            logger.info(f"⚡ Executing as direct command (command too short: {word_count} words, {char_count} chars)")
        
        return trimmed_command
    
    def is_common_command(self, command: str) -> bool:
        """
        Check if a command starts with a common shell command
        
        Args:
            command: The command string to check
            
        Returns:
            bool: True if the command starts with a common shell command
        """
        if not command.strip():
            return False
            
        first_word = command.strip().split()[0].lower()
        return first_word in self.common_commands
    
    def should_wrap_with_claude(self, command: str) -> bool:
        """
        Determine if a command should be wrapped with Claude Code CLI
        
        Args:
            command: The command string to check
            
        Returns:
            bool: True if the command should be wrapped with claude -p
        """
        trimmed_command = command.strip()
        
        if not trimmed_command:
            return False
        
        is_common = self.is_common_command(trimmed_command)
        word_count = len(trimmed_command.split())
        char_count = len(trimmed_command)
        is_long = word_count > self.WORD_COUNT_THRESHOLD or char_count > self.CHAR_COUNT_THRESHOLD
        
        return not is_common and is_long
    
    def get_command_metadata(self, command: str) -> dict:
        """
        Get metadata about a command for logging/debugging purposes
        
        Args:
            command: The command string to analyze
            
        Returns:
            dict: Metadata including word count, char count, common command status, etc.
        """
        trimmed_command = command.strip()
        
        if not trimmed_command:
            return {
                "word_count": 0,
                "char_count": 0,
                "is_common_command": False,
                "first_word": "",
                "should_wrap": False
            }
        
        first_word = trimmed_command.split()[0].lower()
        word_count = len(trimmed_command.split())
        char_count = len(trimmed_command)
        is_common = first_word in self.common_commands
        is_long = word_count > self.WORD_COUNT_THRESHOLD or char_count > self.CHAR_COUNT_THRESHOLD
        should_wrap = not is_common and is_long
        
        return {
            "word_count": word_count,
            "char_count": char_count,
            "is_common_command": is_common,
            "first_word": first_word,
            "should_wrap": should_wrap,
            "is_long_command": is_long
        }