"""Enhanced Daytona service with async streaming capabilities"""

import asyncio
import logging
import time
from contextlib import asynccontextmanager
from datetime import datetime, timedelta
from typing import AsyncGenerator, Dict, Optional, Any, List
import json
import uuid
from dataclasses import dataclass

from daytona import Daytona, DaytonaConfig, SessionExecuteRequest
from models.log_models import LogEntry, LogType, StreamingSession


logger = logging.getLogger(__name__)


def log_daytona_operation(operation: str, **kwargs):
    """Helper function to log Daytona operations with structured format"""
    log_data = {
        "operation": operation,
        "timestamp": datetime.now().isoformat(),
        **kwargs
    }
    logger.info(f"Daytona Operation: {json.dumps(log_data)}")


def log_daytona_response(operation: str, response: Any, duration: float, **kwargs):
    """Helper function to log Daytona responses with timing and content"""
    log_data = {
        "operation": operation,
        "duration_ms": round(duration * 1000, 2),
        "timestamp": datetime.now().isoformat(),
        **kwargs
    }
    
    # Log response details based on type
    if hasattr(response, '__dict__'):
        log_data["response_type"] = type(response).__name__
        
        # Common response attributes
        if hasattr(response, 'exit_code'):
            log_data["exit_code"] = response.exit_code
        if hasattr(response, 'result'):
            log_data["result_length"] = len(str(response.result)) if response.result else 0
            log_data["result_preview"] = str(response.result)[:200] if response.result else None
        if hasattr(response, 'cmd_id'):
            log_data["command_id"] = response.cmd_id
        if hasattr(response, 'artifacts'):
            log_data["has_artifacts"] = response.artifacts is not None
            if response.artifacts:
                log_data["artifact_types"] = list(response.artifacts.__dict__.keys()) if hasattr(response.artifacts, '__dict__') else str(type(response.artifacts))
    else:
        log_data["response"] = str(response)[:500]  # Limit response size in logs
    
    logger.info(f"Daytona Response: {json.dumps(log_data)}")


def log_daytona_error(operation: str, error: Exception, duration: float, **kwargs):
    """Helper function to log Daytona operation errors with context"""
    log_data = {
        "operation": operation,
        "error_type": type(error).__name__,
        "error_message": str(error),
        "duration_ms": round(duration * 1000, 2),
        "timestamp": datetime.now().isoformat(),
        **kwargs
    }
    logger.error(f"Daytona Error: {json.dumps(log_data)}")


class DaytonaStreamingError(Exception):
    """Base exception for Daytona streaming operations"""
    pass


class StreamingConnectionError(DaytonaStreamingError):
    """Raised when streaming connection fails"""
    pass


class SessionTimeoutError(DaytonaStreamingError):
    """Raised when session times out"""
    pass


class SessionManagerError(DaytonaStreamingError):
    """Raised when session management fails"""
    pass


@dataclass
class SessionPool:
    """Connection pool for Daytona sessions"""
    max_sessions: int = 10
    session_timeout: int = 300  # 5 minutes
    cleanup_interval: int = 60  # 1 minute
    
    def __post_init__(self):
        self.active_sessions: Dict[str, StreamingSession] = {}
        self.session_clients: Dict[str, Any] = {}
        self._cleanup_task: Optional[asyncio.Task] = None
        self._lock = asyncio.Lock()
    
    async def start_cleanup_task(self):
        """Start the background cleanup task"""
        if self._cleanup_task is None or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(self._cleanup_expired_sessions())
    
    async def stop_cleanup_task(self):
        """Stop the background cleanup task"""
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
    
    async def _cleanup_expired_sessions(self):
        """Background task to cleanup expired sessions"""
        while True:
            try:
                await asyncio.sleep(self.cleanup_interval)
                await self._remove_expired_sessions()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in session cleanup: {e}")
    
    async def _remove_expired_sessions(self):
        """Remove expired sessions from the pool"""
        async with self._lock:
            current_time = datetime.now()
            expired_sessions = []
            
            for session_id, session in self.active_sessions.items():
                if (current_time - session.created_at).total_seconds() > self.session_timeout:
                    expired_sessions.append(session_id)
            
            for session_id in expired_sessions:
                try:
                    await self._cleanup_session(session_id)
                except Exception as e:
                    logger.error(f"Error cleaning up session {session_id}: {e}")
    
    async def _cleanup_session(self, session_id: str):
        """Cleanup a specific session"""
        if session_id in self.active_sessions:
            logger.info(f"Cleaning up expired session: {session_id}")
            
            # Close client if exists
            if session_id in self.session_clients:
                client = self.session_clients[session_id]
                try:
                    if hasattr(client, 'close'):
                        await client.close()
                except Exception as e:
                    logger.warning(f"Error closing client for session {session_id}: {e}")
                del self.session_clients[session_id]
            
            # Remove session
            del self.active_sessions[session_id]
            logger.info(f"Session {session_id} cleaned up successfully")
    
    async def get_session(self, sandbox_id: str, command: Optional[str] = None) -> str:
        """Get or create a session for streaming"""
        async with self._lock:
            # Check if we can reuse an existing session for this sandbox
            for session_id, session in self.active_sessions.items():
                if (session.sandbox_id == sandbox_id and 
                    session.status == "active" and
                    (datetime.now() - session.created_at).total_seconds() < self.session_timeout):
                    logger.info(f"Reusing existing session: {session_id}")
                    return session_id
            
            # Create new session if under limit
            if len(self.active_sessions) >= self.max_sessions:
                # Remove oldest session to make room
                oldest_session_id = min(
                    self.active_sessions.keys(),
                    key=lambda k: self.active_sessions[k].created_at
                )
                await self._cleanup_session(oldest_session_id)
                logger.info(f"Removed oldest session {oldest_session_id} to make room")
            
            # Create new session
            session_id = str(uuid.uuid4())
            session = StreamingSession(
                session_id=session_id,
                sandbox_id=sandbox_id,
                command=command
            )
            
            self.active_sessions[session_id] = session
            logger.info(f"Created new session: {session_id} for sandbox: {sandbox_id}")
            return session_id
    
    async def release_session(self, session_id: str):
        """Release a session from the pool"""
        async with self._lock:
            if session_id in self.active_sessions:
                self.active_sessions[session_id].status = "completed"
                # Don't immediately remove - let cleanup task handle it
                logger.info(f"Released session: {session_id}")


class DaytonaService:
    """Enhanced Daytona service with async streaming capabilities"""
    
    def __init__(
        self,
        daytona_config: DaytonaConfig,
        max_sessions: int = 10,
        session_timeout: int = 300,
        retry_attempts: int = 3,
        retry_delay: int = 1
    ):
        self.daytona = Daytona(daytona_config)
        self.config = daytona_config
        self.retry_attempts = retry_attempts
        self.retry_delay = retry_delay
        
        # Initialize session pool
        self.session_pool = SessionPool(
            max_sessions=max_sessions,
            session_timeout=session_timeout
        )
        
        # Start cleanup task
        self._initialized = False
    
    async def initialize(self):
        """Initialize the service and start background tasks"""
        if not self._initialized:
            await self.session_pool.start_cleanup_task()
            self._initialized = True
            logger.info("DaytonaService initialized with streaming capabilities")
    
    async def cleanup(self):
        """Cleanup resources and stop background tasks"""
        if self._initialized:
            await self.session_pool.stop_cleanup_task()
            # Cleanup all active sessions
            for session_id in list(self.session_pool.active_sessions.keys()):
                await self.session_pool._cleanup_session(session_id)
            self._initialized = False
            logger.info("DaytonaService cleanup completed")
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.cleanup()
    
    # Backward compatibility methods (synchronous)
    def create_sandbox(self, *args, **kwargs):
        """Create sandbox (synchronous - backward compatibility)"""
        start_time = time.time()
        
        log_daytona_operation(
            "create_sandbox",
            args=args,
            kwargs=kwargs
        )
        
        try:
            response = self.daytona.create(*args, **kwargs)
            duration = time.time() - start_time
            
            log_daytona_response(
                "create_sandbox",
                response,
                duration,
                sandbox_id=getattr(response, 'id', None) if response else None
            )
            
            return response
            
        except Exception as e:
            duration = time.time() - start_time
            log_daytona_error("create_sandbox", e, duration, args=args, kwargs=kwargs)
            raise
    
    def get_sandbox(self, sandbox_id: str):
        """Get sandbox (synchronous - backward compatibility)"""
        start_time = time.time()
        
        log_daytona_operation("get_sandbox", sandbox_id=sandbox_id)
        
        try:
            response = self.daytona.get(sandbox_id)
            duration = time.time() - start_time
            
            log_daytona_response(
                "get_sandbox",
                response,
                duration,
                sandbox_id=sandbox_id
            )
            
            return response
            
        except Exception as e:
            duration = time.time() - start_time
            log_daytona_error("get_sandbox", e, duration, sandbox_id=sandbox_id)
            raise
    
    def delete_sandbox(self, sandbox_id: str):
        """Delete sandbox (synchronous - backward compatibility)"""
        start_time = time.time()
        
        log_daytona_operation("delete_sandbox", sandbox_id=sandbox_id)
        
        try:
            response = self.daytona.delete(sandbox_id)
            duration = time.time() - start_time
            
            log_daytona_response(
                "delete_sandbox",
                response,
                duration,
                sandbox_id=sandbox_id
            )
            
            return response
            
        except Exception as e:
            duration = time.time() - start_time
            log_daytona_error("delete_sandbox", e, duration, sandbox_id=sandbox_id)
            raise
    
    def execute_command(self, sandbox_id: str, command: str, working_directory: str = "/home/<USER>/workspace/repository"):
        """Execute command synchronously (backward compatibility)"""
        start_time = time.time()
        
        log_daytona_operation(
            "execute_command_sync",
            sandbox_id=sandbox_id,
            command=command,
            working_directory=working_directory
        )
        
        try:
            # First get the sandbox
            sandbox_start = time.time()
            sandbox = self.daytona.get(sandbox_id)
            sandbox_duration = time.time() - sandbox_start
            
            logger.debug(f"Retrieved sandbox in {sandbox_duration*1000:.2f}ms")
            
            # Execute the command
            exec_start = time.time()
            response = sandbox.process.exec(command, working_directory)
            exec_duration = time.time() - exec_start
            total_duration = time.time() - start_time
            
            log_daytona_response(
                "execute_command_sync",
                response,
                total_duration,
                sandbox_id=sandbox_id,
                command=command,
                working_directory=working_directory,
                exec_duration_ms=round(exec_duration * 1000, 2),
                sandbox_get_duration_ms=round(sandbox_duration * 1000, 2)
            )
            
            # Log detailed execution result
            if response:
                logger.debug(f"Command execution details - Exit Code: {getattr(response, 'exit_code', 'N/A')}, "
                           f"Result Length: {len(str(getattr(response, 'result', '')))}, "
                           f"Has Artifacts: {hasattr(response, 'artifacts') and response.artifacts is not None}")
            
            return response
            
        except Exception as e:
            duration = time.time() - start_time
            log_daytona_error(
                "execute_command_sync", 
                e, 
                duration, 
                sandbox_id=sandbox_id,
                command=command,
                working_directory=working_directory
            )
            raise
    
    # New async streaming methods
    async def execute_command_stream(
        self, 
        sandbox_id: str, 
        command: str,
        working_directory: str = "/home/<USER>/workspace/repository"
    ) -> AsyncGenerator[LogEntry, None]:
        """
        Execute command and stream logs in real-time.
        
        Args:
            sandbox_id: ID of the sandbox
            command: Command to execute
            working_directory: Working directory for command execution
            
        Yields:
            LogEntry: Individual log entries with timestamp, type, and content
        """
        session_id = None
        start_time = time.time()
        
        try:
            # Get or create session
            session_start = time.time()
            session_id = await self.session_pool.get_session(sandbox_id, command)
            session_duration = time.time() - session_start
            
            log_daytona_operation(
                "execute_command_stream",
                sandbox_id=sandbox_id,
                command=command,
                working_directory=working_directory,
                session_id=session_id,
                session_creation_duration_ms=round(session_duration * 1000, 2)
            )
            
            # Yield start event
            yield LogEntry(
                timestamp=datetime.now(),
                log_type=LogType.START,
                content=f"Starting command execution: {command}",
                metadata={
                    "session_id": session_id, 
                    "sandbox_id": sandbox_id,
                    "session_duration_ms": round(session_duration * 1000, 2)
                }
            )
            
            # Check if this is a Claude command (which doesn't support streaming)
            is_claude_command = "claude -p" in command
            
            if is_claude_command:
                logger.info(f"Detected Claude command - using hybrid streaming approach")
                async for log_entry in self._execute_claude_hybrid(sandbox_id, command, working_directory, session_id):
                    yield log_entry
            else:
                # Try async streaming for non-Claude commands
                streaming_start = time.time()
                try:
                    logger.debug(f"Attempting async streaming for session {session_id}")
                    async for log_entry in self._stream_command_async(sandbox_id, command, working_directory, session_id):
                        yield log_entry
                    
                    streaming_duration = time.time() - streaming_start
                    logger.info(f"Async streaming completed successfully in {streaming_duration*1000:.2f}ms")
                    
                except (StreamingConnectionError, NotImplementedError) as e:
                    streaming_duration = time.time() - streaming_start
                    
                    log_daytona_error(
                        "async_streaming_failed",
                        e,
                        streaming_duration,
                        session_id=session_id,
                        sandbox_id=sandbox_id,
                        command=command
                    )
                    
                    logger.warning(f"Async streaming failed after {streaming_duration*1000:.2f}ms, falling back to sync: {e}")
                    
                    # Fallback to synchronous execution with polling
                    fallback_start = time.time()
                    async for log_entry in self._stream_command_sync_fallback(sandbox_id, command, working_directory):
                        yield log_entry
                    
                    fallback_duration = time.time() - fallback_start
                    logger.info(f"Sync fallback completed in {fallback_duration*1000:.2f}ms")
            
            # Yield end event
            total_duration = time.time() - start_time
            yield LogEntry(
                timestamp=datetime.now(),
                log_type=LogType.END,
                content="Command execution completed",
                metadata={
                    "session_id": session_id, 
                    "sandbox_id": sandbox_id,
                    "total_duration_ms": round(total_duration * 1000, 2)
                }
            )
            
            log_daytona_response(
                "execute_command_stream_completed",
                {"status": "success"},
                total_duration,
                session_id=session_id,
                sandbox_id=sandbox_id,
                command=command
            )
            
        except Exception as e:
            total_duration = time.time() - start_time
            
            log_daytona_error(
                "execute_command_stream",
                e,
                total_duration,
                session_id=session_id,
                sandbox_id=sandbox_id,
                command=command,
                working_directory=working_directory
            )
            
            logger.error(f"Error in execute_command_stream after {total_duration*1000:.2f}ms: {e}")
            
            # Yield error event
            yield LogEntry(
                timestamp=datetime.now(),
                log_type=LogType.ERROR,
                content=f"Command execution failed: {str(e)}",
                metadata={
                    "session_id": session_id, 
                    "sandbox_id": sandbox_id, 
                    "error": str(e),
                    "duration_ms": round(total_duration * 1000, 2)
                }
            )
            
        finally:
            # Release session
            if session_id:
                await self.session_pool.release_session(session_id)
    
    async def _stream_command_async(
        self, 
        sandbox_id: str, 
        command: str, 
        working_directory: str,
        session_id: str
    ) -> AsyncGenerator[LogEntry, None]:
        """Stream command using Daytona SDK session-based async streaming"""
        
        retry_count = 0
        daytona_session_id = None
        
        while retry_count < self.retry_attempts:
            attempt_start = time.time()
            try:
                # Get sandbox
                sandbox_start = time.time()
                sandbox = self.daytona.get(sandbox_id)
                sandbox_duration = time.time() - sandbox_start
                
                logger.debug(f"Retrieved sandbox for streaming in {sandbox_duration*1000:.2f}ms")
                
                # Create a unique Daytona session ID
                daytona_session_id = f"stream-{session_id}-{uuid.uuid4().hex[:8]}"
                
                log_daytona_operation(
                    "create_daytona_session",
                    daytona_session_id=daytona_session_id,
                    session_id=session_id,
                    sandbox_id=sandbox_id,
                    retry_attempt=retry_count + 1
                )
                
                session_create_start = time.time()
                sandbox.process.create_session(daytona_session_id)
                session_create_duration = time.time() - session_create_start
                
                logger.info(f"Created Daytona session {daytona_session_id} in {session_create_duration*1000:.2f}ms")
                
                # Execute command asynchronously using SessionExecuteRequest
                execute_start = time.time()
                
                log_daytona_operation(
                    "execute_session_command",
                    daytona_session_id=daytona_session_id,
                    command=command,
                    session_id=session_id,
                    sandbox_id=sandbox_id
                )
                
                cmd_result = sandbox.process.execute_session_command(
                    daytona_session_id,
                    SessionExecuteRequest(
                        command=command,
                        run_async=True  # Use run_async instead of deprecated var_async
                    )
                )
                execute_duration = time.time() - execute_start
                
                log_daytona_response(
                    "execute_session_command",
                    cmd_result,
                    execute_duration,
                    daytona_session_id=daytona_session_id,
                    command_id=getattr(cmd_result, 'cmd_id', None),
                    session_id=session_id,
                    sandbox_id=sandbox_id
                )
                
                logger.info(f"Command started with ID: {cmd_result.cmd_id} in {execute_duration*1000:.2f}ms")
                
                # Stream logs in real-time using async callback
                log_buffer = []
                command_completed = False
                chunk_count = 0
                total_log_size = 0
                
                def log_callback(chunk: str):
                    """Callback function to handle streaming log chunks"""
                    nonlocal chunk_count, total_log_size
                    
                    chunk_count += 1
                    logger.info(f"📝 Chunk {chunk_count}: '{chunk.strip()}'")
                    
                    if chunk.strip():
                        total_log_size += len(chunk)
                        log_buffer.append(chunk.strip())
                
                # Start streaming logs asynchronously 
                stream_start = time.time()
                try:
                    log_daytona_operation(
                        "start_log_streaming",
                        daytona_session_id=daytona_session_id,
                        command_id=cmd_result.cmd_id,
                        session_id=session_id
                    )
                    
                    logger.info(f"Starting log streaming - Session: {daytona_session_id}, Command: {cmd_result.cmd_id}")
                    logger.info(f"Callback function created, calling get_session_command_logs_async with timeout...")
                    
                    # Stream logs with appropriate timeout for Claude commands
                    try:
                        await asyncio.wait_for(
                            sandbox.process.get_session_command_logs_async(
                                daytona_session_id, 
                                cmd_result.cmd_id, 
                                log_callback
                            ),
                            timeout=300.0  # 5 minute timeout for Claude commands
                        )
                        logger.info(f"Log streaming completed normally")
                    except asyncio.TimeoutError:
                        logger.warning(f"Log streaming timed out after 300s - Claude command may still be running")
                        logger.info(f"Received {chunk_count} chunks before timeout")
                        # Continue with what we have - this is not necessarily an error
                    except Exception as stream_exception:
                        logger.error(f"Log streaming failed with exception: {stream_exception}")
                        # Continue with what we have
                    
                    logger.info(f"get_session_command_logs_async completed - received {chunk_count} chunks")
                    command_completed = True
                    stream_duration = time.time() - stream_start
                    
                    log_daytona_response(
                        "log_streaming_completed",
                        {"chunks_received": chunk_count, "total_bytes": total_log_size},
                        stream_duration,
                        daytona_session_id=daytona_session_id,
                        command_id=cmd_result.cmd_id,
                        session_id=session_id
                    )
                    
                except Exception as stream_error:
                    stream_duration = time.time() - stream_start
                    
                    log_daytona_error(
                        "log_streaming_failed",
                        stream_error,
                        stream_duration,
                        daytona_session_id=daytona_session_id,
                        command_id=cmd_result.cmd_id,
                        session_id=session_id,
                        chunks_received=chunk_count,
                        total_bytes=total_log_size
                    )
                    
                    logger.error(f"Error during log streaming after {stream_duration*1000:.2f}ms: {stream_error}")
                    # Continue to yield any buffered logs
                
                # If we got no logs from streaming, try polling approach
                if len(log_buffer) == 0:
                    logger.warning("No logs received from streaming callback, trying polling approach...")
                    await self._try_polling_logs(sandbox, daytona_session_id, cmd_result.cmd_id, log_buffer)
                
                # Yield all buffered log entries
                logger.info(f"Yielding {len(log_buffer)} log entries from buffer")
                for i, log_line in enumerate(log_buffer):
                    yield LogEntry(
                        timestamp=datetime.now(),
                        log_type=LogType.STDOUT,
                        content=log_line,
                        metadata={
                            "session_id": session_id, 
                            "daytona_session_id": daytona_session_id,
                            "command_id": cmd_result.cmd_id,
                            "chunk_index": i + 1,
                            "total_chunks": len(log_buffer)
                        }
                    )
                
                # If streaming completed successfully, return
                if command_completed:
                    attempt_duration = time.time() - attempt_start
                    logger.info(f"Command streaming completed successfully in {attempt_duration*1000:.2f}ms "
                              f"(chunks: {chunk_count}, bytes: {total_log_size})")
                    
                    log_daytona_response(
                        "streaming_attempt_success",
                        {"chunks": chunk_count, "bytes": total_log_size, "buffer_entries": len(log_buffer)},
                        attempt_duration,
                        daytona_session_id=daytona_session_id,
                        session_id=session_id,
                        sandbox_id=sandbox_id,
                        retry_attempt=retry_count + 1
                    )
                    
                    return
                else:
                    raise StreamingConnectionError("Log streaming did not complete properly")
                
            except Exception as e:
                retry_count += 1
                attempt_duration = time.time() - attempt_start
                
                log_daytona_error(
                    "streaming_attempt_failed",
                    e,
                    attempt_duration,
                    daytona_session_id=daytona_session_id,
                    session_id=session_id,
                    sandbox_id=sandbox_id,
                    retry_attempt=retry_count,
                    max_attempts=self.retry_attempts
                )
                
                logger.error(f"Streaming attempt {retry_count} failed after {attempt_duration*1000:.2f}ms: {e}")
                
                # Clean up session if it was created
                if daytona_session_id:
                    try:
                        # Note: Daytona SDK may not have explicit session cleanup
                        # This is for future-proofing
                        logger.debug(f"Session cleanup placeholder for {daytona_session_id}")
                        pass
                    except Exception as cleanup_error:
                        logger.warning(f"Failed to cleanup session {daytona_session_id}: {cleanup_error}")
                
                if retry_count >= self.retry_attempts:
                    total_retry_duration = time.time() - (attempt_start - attempt_duration)  # Approximate total time
                    
                    log_daytona_error(
                        "streaming_max_retries_exceeded",
                        e,
                        total_retry_duration,
                        session_id=session_id,
                        sandbox_id=sandbox_id,
                        total_attempts=retry_count,
                        command=command
                    )
                    
                    raise StreamingConnectionError(f"Failed to stream command after {self.retry_attempts} attempts: {e}")
                
                # Wait before retry with exponential backoff
                retry_delay = self.retry_delay * (2 ** (retry_count - 1))
                logger.warning(f"Retrying command streaming in {retry_delay}s: {retry_count}/{self.retry_attempts}")
                await asyncio.sleep(retry_delay)
    
    async def _try_polling_logs(self, sandbox, session_id: str, command_id: str, log_buffer: list):
        """Alternative approach: Poll for logs when streaming callback fails"""
        try:
            logger.info(f"Attempting to poll logs for session {session_id}, command {command_id}")
            
            # Try to get logs synchronously as a fallback
            # Note: This may not be available in all SDK versions
            if hasattr(sandbox.process, 'get_session_command_logs'):
                try:
                    # Try synchronous log retrieval
                    logs = sandbox.process.get_session_command_logs(session_id, command_id)
                    if logs:
                        log_buffer.extend(logs.split('\n') if isinstance(logs, str) else [str(logs)])
                        logger.info(f"Retrieved {len(log_buffer)} lines via polling")
                except Exception as e:
                    logger.warning(f"Polling approach also failed: {e}")
            
            # Alternative: Check if command is still running and wait a bit
            await asyncio.sleep(2)  # Give command time to produce output
            
        except Exception as e:
            logger.error(f"Error in polling logs: {e}")
    
    async def _execute_claude_hybrid(
        self,
        sandbox_id: str,
        command: str,
        working_directory: str,
        session_id: str
    ) -> AsyncGenerator[LogEntry, None]:
        """
        Hybrid approach for Claude commands: execute synchronously but simulate streaming
        """
        start_time = time.time()
        
        log_daytona_operation(
            "claude_hybrid_execution",
            sandbox_id=sandbox_id,
            command=command,
            working_directory=working_directory,
            session_id=session_id
        )
        
        try:
            # Show progress indicators while Claude executes
            progress_messages = [
                "🤖 Claude is analyzing your request...",
                "💭 Processing your query...", 
                "📝 Generating response...",
                "🔍 Reviewing and refining...",
                "✨ Finalizing response..."
            ]
            
            # Execute Claude synchronously in the background
            result_task = asyncio.create_task(
                asyncio.to_thread(self.execute_command, sandbox_id, command, working_directory)
            )
            
            # Show progress while waiting for result
            progress_index = 0
            while not result_task.done():
                if progress_index < len(progress_messages):
                    yield LogEntry(
                        timestamp=datetime.now(),
                        log_type=LogType.SYSTEM,
                        content=progress_messages[progress_index],
                        metadata={"session_id": session_id, "progress": True}
                    )
                    progress_index += 1
                
                try:
                    await asyncio.wait_for(asyncio.shield(result_task), timeout=2.0)
                    break  # Task completed
                except asyncio.TimeoutError:
                    continue  # Keep showing progress
            
            # Get the result
            response = await result_task
            
            # Process and stream the result
            total_duration = time.time() - start_time
            
            if response and response.result:
                # Stream the result in chunks to simulate real-time output
                result_text = response.result.strip()
                
                # Split into sentences or logical chunks
                chunks = self._chunk_text(result_text)
                
                logger.info(f"Streaming Claude result in {len(chunks)} chunks")
                
                for i, chunk in enumerate(chunks):
                    yield LogEntry(
                        timestamp=datetime.now(),
                        log_type=LogType.STDOUT,
                        content=chunk,
                        metadata={
                            "session_id": session_id,
                            "sandbox_id": sandbox_id,
                            "chunk_index": i + 1,
                            "total_chunks": len(chunks),
                            "claude_hybrid": True,
                            "exit_code": response.exit_code
                        }
                    )
                    
                    # Small delay between chunks to simulate streaming
                    await asyncio.sleep(0.1)
            
            log_daytona_response(
                "claude_hybrid_completed",
                {"chunks_streamed": len(chunks) if response and response.result else 0},
                total_duration,
                session_id=session_id,
                sandbox_id=sandbox_id,
                command=command
            )
            
        except Exception as e:
            duration = time.time() - start_time
            
            log_daytona_error(
                "claude_hybrid_failed",
                e,
                duration,
                session_id=session_id,
                sandbox_id=sandbox_id,
                command=command
            )
            
            yield LogEntry(
                timestamp=datetime.now(),
                log_type=LogType.ERROR,
                content=f"Claude execution failed: {str(e)}",
                metadata={
                    "session_id": session_id,
                    "sandbox_id": sandbox_id,
                    "error": str(e),
                    "claude_hybrid": True
                }
            )
    
    
    def _chunk_text(self, text: str, max_chunk_size: int = 200) -> list:
        """Split text into logical chunks for streaming presentation"""
        if not text:
            return []
        
        # Try to split by sentences first
        sentences = text.replace('. ', '.\n').replace('! ', '!\n').replace('? ', '?\n').split('\n')
        
        chunks = []
        current_chunk = ""
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
                
            if len(current_chunk + sentence) <= max_chunk_size:
                current_chunk += sentence + " "
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = sentence + " "
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return chunks if chunks else [text]
    
    async def _stream_command_sync_fallback(
        self, 
        sandbox_id: str, 
        command: str, 
        working_directory: str
    ) -> AsyncGenerator[LogEntry, None]:
        """Fallback to synchronous execution when streaming fails"""
        
        fallback_start = time.time()
        
        log_daytona_operation(
            "sync_fallback_start",
            sandbox_id=sandbox_id,
            command=command,
            working_directory=working_directory
        )
        
        try:
            # Execute synchronously
            exec_start = time.time()
            response = self.execute_command(sandbox_id, command, working_directory)
            exec_duration = time.time() - exec_start
            
            logger.info(f"Sync fallback execution completed in {exec_duration*1000:.2f}ms")
            
            # Convert result to streaming format
            line_count = 0
            if response.result:
                lines = response.result.split('\n')
                logger.info(f"Converting sync response to streaming format: {len(lines)} lines")
                
                for line in lines:
                    if line.strip():
                        line_count += 1
                        yield LogEntry(
                            timestamp=datetime.now(),
                            log_type=LogType.STDOUT,
                            content=line,
                            metadata={
                                "fallback": True, 
                                "exit_code": response.exit_code,
                                "line_number": line_count,
                                "total_lines": len([l for l in lines if l.strip()])
                            }
                        )
                        # Add small delay to simulate streaming
                        await asyncio.sleep(0.05)
            
            # Yield final status
            if response.exit_code != 0:
                yield LogEntry(
                    timestamp=datetime.now(),
                    log_type=LogType.ERROR,
                    content=f"Command failed with exit code: {response.exit_code}",
                    metadata={"exit_code": response.exit_code, "fallback": True}
                )
            
            fallback_duration = time.time() - fallback_start
            
            log_daytona_response(
                "sync_fallback_completed",
                {"lines_yielded": line_count, "exit_code": response.exit_code},
                fallback_duration,
                sandbox_id=sandbox_id,
                command=command,
                exec_duration_ms=round(exec_duration * 1000, 2)
            )
        
        except Exception as e:
            fallback_duration = time.time() - fallback_start
            
            log_daytona_error(
                "sync_fallback_failed",
                e,
                fallback_duration,
                sandbox_id=sandbox_id,
                command=command,
                working_directory=working_directory
            )
            
            logger.error(f"Synchronous fallback failed after {fallback_duration*1000:.2f}ms: {e}")
            yield LogEntry(
                timestamp=datetime.now(),
                log_type=LogType.ERROR,
                content=f"Fallback execution failed: {str(e)}",
                metadata={"error": str(e), "fallback": True}
            )
    
    async def get_session_logs_stream(
        self, 
        session_id: str
    ) -> AsyncGenerator[LogEntry, None]:
        """
        Stream logs from existing session.
        
        Args:
            session_id: ID of the streaming session
            
        Yields:
            LogEntry: Log entries from the session
        """
        
        if session_id not in self.session_pool.active_sessions:
            raise SessionManagerError(f"Session {session_id} not found")
        
        session = self.session_pool.active_sessions[session_id]
        
        # This would be the actual implementation using Daytona SDK
        # For now, we'll return a placeholder implementation
        yield LogEntry(
            timestamp=datetime.now(),
            log_type=LogType.SYSTEM,
            content=f"Streaming logs for session: {session_id}",
            metadata={"session_id": session_id, "sandbox_id": session.sandbox_id}
        )
    
    # Session management methods
    async def list_active_sessions(self) -> List[Dict[str, Any]]:
        """List all active streaming sessions"""
        start_time = time.time()
        
        try:
            sessions = [session.to_dict() for session in self.session_pool.active_sessions.values()]
            duration = time.time() - start_time
            
            log_daytona_response(
                "list_active_sessions",
                {"session_count": len(sessions)},
                duration
            )
            
            logger.debug(f"Listed {len(sessions)} active sessions in {duration*1000:.2f}ms")
            return sessions
            
        except Exception as e:
            duration = time.time() - start_time
            log_daytona_error("list_active_sessions", e, duration)
            raise
    
    async def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific session"""
        start_time = time.time()
        
        log_daytona_operation("get_session_info", session_id=session_id)
        
        try:
            if session_id in self.session_pool.active_sessions:
                session_info = self.session_pool.active_sessions[session_id].to_dict()
                duration = time.time() - start_time
                
                log_daytona_response(
                    "get_session_info",
                    {"found": True, "session_data": session_info},
                    duration,
                    session_id=session_id
                )
                
                return session_info
            else:
                duration = time.time() - start_time
                
                log_daytona_response(
                    "get_session_info",
                    {"found": False},
                    duration,
                    session_id=session_id
                )
                
                logger.debug(f"Session {session_id} not found")
                return None
                
        except Exception as e:
            duration = time.time() - start_time
            log_daytona_error("get_session_info", e, duration, session_id=session_id)
            raise
    
    async def close_session(self, session_id: str):
        """Manually close a specific session"""
        start_time = time.time()
        
        log_daytona_operation("close_session", session_id=session_id)
        
        try:
            await self.session_pool._cleanup_session(session_id)
            duration = time.time() - start_time
            
            log_daytona_response(
                "close_session",
                {"status": "closed"},
                duration,
                session_id=session_id
            )
            
            logger.info(f"Session {session_id} closed successfully in {duration*1000:.2f}ms")
            
        except Exception as e:
            duration = time.time() - start_time
            log_daytona_error("close_session", e, duration, session_id=session_id)
            raise