#!/usr/bin/env python3
"""
Test script for Daytona log streaming based on official documentation
https://www.daytona.io/docs/log-streaming/
"""

import asyncio
import os
import logging
from datetime import datetime
from daytona import Daytona, DaytonaConfig, SessionExecuteRequest

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_daytona_streaming():
    """Test Daytona streaming exactly as per documentation"""
    
    # Get credentials from environment
    api_key = os.getenv('DAYTONA_API_KEY')
    api_url = os.getenv('DAYTONA_API_URL', 'https://api.daytona.io')
    target = os.getenv('DAYTONA_TARGET', 'us')
    
    if not api_key:
        logger.error("DAYTONA_API_KEY environment variable not set!")
        return
    
    logger.info(f"🚀 Starting Daytona streaming test")
    logger.info(f"📡 API URL: {api_url}")
    logger.info(f"🎯 Target: {target}")
    logger.info(f"🔑 API Key: {api_key[:10]}...")
    
    try:
        # Initialize Daytona client
        config = DaytonaConfig(
            api_key=api_key,
            api_url=api_url,
            target=target
        )
        daytona = Daytona(config)
        logger.info("✅ Daytona client initialized")
        
        # Use existing sandbox ID from our tests
        sandbox_id = "559a67f7-816b-4fec-b2e4-cdb0c02a8153"
        logger.info(f"🏗️ Using existing sandbox: {sandbox_id}")
        
        # Get sandbox
        sandbox = daytona.get(sandbox_id)
        logger.info("✅ Sandbox retrieved successfully")
        
        # Create session
        session_id = f"test-streaming-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
        logger.info(f"🔧 Creating session: {session_id}")
        sandbox.process.create_session(session_id)
        logger.info("✅ Session created successfully")
        
        # Test with a simpler command first to verify streaming works
        command_text = 'claude -p "Tell me about this project"'
        logger.info(f"⚡ Executing test command: {command_text}")
        
        cmd_result = sandbox.process.execute_session_command(
            session_id,
            SessionExecuteRequest(
                command=command_text,
                run_async=True  # Use run_async instead of deprecated var_async
            )
        )
        logger.info(f"✅ Command started with ID: {cmd_result.cmd_id}")
        
        # Set up log callback
        logs_received = []
        chunk_count = 0
        
        def log_callback(chunk: str):
            nonlocal chunk_count
            chunk_count += 1
            logger.info(f"📝 Chunk {chunk_count}: '{chunk.strip()}'")
            if chunk.strip():
                logs_received.append(chunk.strip())
        
        # Stream logs asynchronously (exactly as per documentation)
        logger.info("🌊 Starting log streaming...")
        start_time = datetime.now()
        
        try:
            # Add timeout to prevent hanging (Claude commands can take longer)
            await asyncio.wait_for(
                sandbox.process.get_session_command_logs_async(
                    session_id, 
                    cmd_result.cmd_id, 
                    log_callback
                ),
                timeout=180.0  # 3 minute timeout for Claude commands
            )
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.info(f"✅ Log streaming completed in {duration:.2f} seconds")
            logger.info(f"📊 Total chunks received: {chunk_count}")
            logger.info(f"📋 Total log lines: {len(logs_received)}")
            
            # Print all received logs
            if logs_received:
                logger.info("📜 Received logs:")
                for i, log_line in enumerate(logs_received, 1):
                    logger.info(f"  {i}: {log_line}")
            else:
                logger.warning("⚠️ No logs received!")
                
        except asyncio.TimeoutError:
            logger.error(f"⏰ Streaming timed out after 180 seconds")
            logger.info(f"📊 Chunks received before timeout: {chunk_count}")
            logger.info(f"📋 Log lines before timeout: {len(logs_received)}")
            
        except Exception as e:
            logger.error(f"❌ Streaming failed with error: {e}")
            logger.info(f"📊 Chunks received before error: {chunk_count}")
            
        # Clean up
        logger.info("🧹 Cleaning up...")
        # Note: Session cleanup may not be available in all SDK versions
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        logger.error(traceback.format_exc())

async def main():
    """Main test function"""
    logger.info("🧪 Daytona Log Streaming Test")
    logger.info("=" * 50)
    
    await test_daytona_streaming()
    
    logger.info("=" * 50)
    logger.info("🏁 Test completed")

if __name__ == "__main__":
    asyncio.run(main())