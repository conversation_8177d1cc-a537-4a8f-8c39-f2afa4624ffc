# Technical Documentation: VoiceCode Daytona Streaming Integration

## Overview

This document provides comprehensive technical reference for the VoiceCode backend's Daytona streaming integration, including the hybrid Claude CLI solution that addresses streaming limitations.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Core Components](#core-components)
3. [Streaming Implementation](#streaming-implementation)
4. [Claude CLI Integration](#claude-cli-integration)
5. [Enhanced Logging System](#enhanced-logging-system)
6. [Configuration](#configuration)
7. [API Reference](#api-reference)
8. [Troubleshooting](#troubleshooting)
9. [Performance Considerations](#performance-considerations)

## Architecture Overview

### System Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Daytona       │
│   (React)       │────┤   (FastAPI)     │────┤   Sandbox       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │  Claude CLI     │
                       │  (In Sandbox)   │
                       └─────────────────┘
```

### Streaming Flow

```
User Request → Command Processing → Execution Routing
                                          │
                     ┌────────────────────┼────────────────────┐
                     ▼                    ▼                    ▼
            Regular Commands        Claude Commands      Error Handling
            (Native Streaming)      (Hybrid Execution)   (Fallback)
                     │                    │                    │
                     ▼                    ▼                    ▼
            Real-time Chunks        Progress + Chunked      Error Messages
                     │               Response                  │
                     └────────────────────┼────────────────────┘
                                          ▼
                                 SSE Stream to Frontend
```

## Core Components

### 1. DaytonaService (`services/daytona_service.py`)

**Purpose**: Main service for Daytona sandbox communication with streaming capabilities.

**Key Features**:
- Session pool management
- Hybrid streaming for Claude commands
- Enhanced logging and monitoring
- Automatic fallback mechanisms

**Methods**:
- `execute_command_stream()`: Main streaming interface
- `_execute_claude_hybrid()`: Hybrid Claude execution
- `_stream_command_async()`: Native Daytona streaming
- `_stream_command_sync_fallback()`: Synchronous fallback

### 2. CommandProcessor (`services/command_processor.py`)

**Purpose**: Intelligent command analysis and Claude CLI integration.

**Key Features**:
- Command classification (shell vs natural language)
- Claude CLI wrapper generation
- Optimized output formatting

### 3. Streaming Models (`models/log_models.py`)

**Purpose**: Data structures for streaming communication.

**Key Types**:
- `LogEntry`: Individual log/output entries
- `LogType`: Classification (START, STDOUT, ERROR, END, SYSTEM)
- `StreamingSession`: Session management data

## Streaming Implementation

### Native Daytona Streaming

For regular shell commands that support real-time output:

```python
async def _stream_command_async(self, sandbox_id, command, working_directory, session_id):
    # Create Daytona session
    daytona_session_id = f"stream-{session_id}-{uuid.uuid4().hex[:8]}"
    sandbox.process.create_session(daytona_session_id)
    
    # Execute with async flag
    cmd_result = sandbox.process.execute_session_command(
        daytona_session_id,
        SessionExecuteRequest(command=command, run_async=True)
    )
    
    # Stream logs with callback
    def log_callback(chunk: str):
        if chunk.strip():
            log_buffer.append(chunk.strip())
    
    await sandbox.process.get_session_command_logs_async(
        daytona_session_id, cmd_result.cmd_id, log_callback
    )
```

### Hybrid Claude Execution

For Claude CLI commands that don't support streaming:

```python
async def _execute_claude_hybrid(self, sandbox_id, command, working_directory, session_id):
    # Progress indicators
    progress_messages = [
        "🤖 Claude is analyzing your request...",
        "💭 Processing your query...",
        "📝 Generating response...",
        "🔍 Reviewing and refining...",
        "✨ Finalizing response..."
    ]
    
    # Execute synchronously with progress
    result_task = asyncio.create_task(
        asyncio.to_thread(self.execute_command, sandbox_id, command, working_directory)
    )
    
    # Show progress while waiting
    while not result_task.done():
        yield LogEntry(log_type=LogType.SYSTEM, content=progress_messages[index])
        await asyncio.sleep(2.0)
    
    # Chunk and stream final result
    response = await result_task
    chunks = self._chunk_text(response.result)
    for chunk in chunks:
        yield LogEntry(log_type=LogType.STDOUT, content=chunk)
        await asyncio.sleep(0.1)  # Simulate streaming
```

## Claude CLI Integration

### Command Detection

The system automatically detects Claude commands using pattern matching:

```python
is_claude_command = "claude -p" in command
```

### Command Processing

Claude commands are wrapped with optimized parameters:

```python
def process_command(self, command: str) -> str:
    if should_wrap_with_claude:
        escaped_command = command.replace('"', '\\"')
        return f'stdbuf -oL -eL claude -p "{escaped_command}" --output-format text 2>&1'
    return command
```

### Known Claude CLI Limitations

| Issue | Description | Solution |
|-------|-------------|----------|
| **No Streaming Output** | Claude CLI buffers output until completion | Hybrid execution with progress indicators |
| **TTY Dependency** | Designed for interactive terminals | Force unbuffered output with `stdbuf` |
| **Long Execution Time** | Can take minutes to process | Extended timeouts (5 minutes) |
| **Deprecated Parameters** | `var_async` → `run_async` | Updated to current API |

## Enhanced Logging System

### Structured Logging Helpers

```python
def log_daytona_operation(operation: str, **kwargs):
    """Log operation start with structured data"""
    log_data = {
        "operation": operation,
        "timestamp": datetime.now().isoformat(),
        **kwargs
    }
    logger.info(f"Daytona Operation: {json.dumps(log_data)}")

def log_daytona_response(operation: str, response: Any, duration: float, **kwargs):
    """Log operation completion with timing and content analysis"""
    log_data = {
        "operation": operation,
        "duration_ms": round(duration * 1000, 2),
        "response_type": type(response).__name__,
        **kwargs
    }
    logger.info(f"Daytona Response: {json.dumps(log_data)}")
```

### Log Categories

| Category | Purpose | Example |
|----------|---------|---------|
| **Daytona Operation** | Operation initiation | Session creation, command execution |
| **Daytona Response** | Operation completion | Response analysis, timing metrics |
| **Daytona Error** | Error conditions | Timeout, connection issues, SDK errors |

### Sample Log Output

```json
{
  "operation": "execute_command_stream",
  "timestamp": "2025-08-01T07:22:20.982465",
  "sandbox_id": "559a67f7-816b-4fec-b2e4-cdb0c02a8153",
  "command": "claude -p \"Tell me about the current project\"",
  "session_id": "62aaa1f9-0ca1-46c1-a4d0-51bc3c056672",
  "session_creation_duration_ms": 0.08
}
```

## Configuration

### Environment Variables

```bash
# Daytona Configuration
DAYTONA_API_KEY=dtn_xxx...
DAYTONA_API_URL=https://app.daytona.io/api
DAYTONA_TARGET=us
DAYTONA_ORG_ID=xxx...
DAYTONA_IMAGE=voicecode-claude:1.0.6
```

### Service Parameters

```python
DaytonaService(
    daytona_config=config,
    max_sessions=10,           # Maximum concurrent sessions
    session_timeout=300,       # Session timeout (5 minutes)
    retry_attempts=3,          # Retry attempts for failed operations
    retry_delay=1              # Base retry delay (exponential backoff)
)
```

### Timeouts

| Operation | Timeout | Reason |
|-----------|---------|--------|
| **Regular Commands** | 120s | Most shell commands complete quickly |
| **Claude Commands** | 300s | Claude processing can take several minutes |
| **Session Creation** | 30s | Network operation |
| **Streaming Callback** | 300s | Wait for command completion |

## API Reference

### Streaming Endpoint

```http
POST /api/sandbox/{sandbox_id}/tasks/stream
Content-Type: application/json
Accept: text/event-stream

{
  "command": "Tell me about the current project",
  "working_directory": "/home/<USER>/workspace/repository"
}
```

### Response Format (SSE)

```
data: {"id": "msg-1", "role": "assistant", "content": "🤖 Claude is analyzing...", "metadata": {"type": "progress"}}

data: {"id": "msg-2", "role": "assistant", "content": "This project appears to be...", "metadata": {"type": "output"}}

data: [DONE]
```

### LogEntry Structure

```python
@dataclass
class LogEntry:
    timestamp: datetime
    log_type: LogType  # START, STDOUT, STDERR, ERROR, END, SYSTEM
    content: str
    metadata: Dict[str, Any] = None
```

## Troubleshooting

### Common Issues

#### 1. Streaming Hangs/Timeouts

**Symptoms**: Request hangs, no output received
**Diagnosis**: Check logs for "Log streaming timed out"
**Solutions**:
- Verify sandbox is running
- Check Daytona API connectivity
- Increase timeout for long-running commands

#### 2. Claude Commands Not Working

**Symptoms**: Claude commands fail or produce no output
**Diagnosis**: Look for "claude_hybrid_execution" in logs
**Solutions**:
- Verify Claude CLI is installed in sandbox
- Check Claude authentication
- Review command escaping

#### 3. Session Pool Exhaustion

**Symptoms**: "Maximum sessions reached" errors
**Diagnosis**: Check active session count in logs
**Solutions**:
- Increase `max_sessions` parameter
- Reduce `session_timeout`
- Implement session cleanup

### Debug Commands

```bash
# Check backend logs
docker compose logs --tail=100 --follow voicecode-api

# Test Daytona connectivity
curl -H "Authorization: Bearer $DAYTONA_API_KEY" $DAYTONA_API_URL/sandboxes

# Verify sandbox status
curl -H "Authorization: Bearer $DAYTONA_API_KEY" $DAYTONA_API_URL/sandboxes/{sandbox_id}
```

### Log Analysis

Search for these patterns in logs:

```bash
# Command execution flow
grep "Daytona Operation.*execute_command_stream" logs.txt

# Claude hybrid execution
grep "claude_hybrid_execution" logs.txt

# Streaming issues
grep "streaming.*failed\|timeout" logs.txt

# Performance metrics
grep "duration_ms" logs.txt
```

## Performance Considerations

### Optimization Strategies

1. **Session Reuse**: Sessions are pooled and reused when possible
2. **Concurrent Execution**: Multiple commands can stream simultaneously
3. **Intelligent Routing**: Claude commands bypass streaming attempts
4. **Progressive Loading**: Large responses are chunked for better UX

### Metrics to Monitor

| Metric | Threshold | Action |
|--------|-----------|---------|
| **Session Creation Time** | >1000ms | Check network/API latency |
| **Command Execution Time** | >30s (regular), >300s (Claude) | Investigate sandbox performance |
| **Active Sessions** | >80% of max | Scale session pool |
| **Error Rate** | >5% | Review error logs, check connectivity |

### Resource Usage

- **Memory**: ~50MB per active session
- **Network**: Persistent WebSocket connections for streaming
- **CPU**: Minimal overhead, mostly I/O bound

## Integration Examples

### Basic Streaming

```python
async def stream_command(sandbox_id: str, command: str):
    async with DaytonaService(config) as service:
        async for log_entry in service.execute_command_stream(sandbox_id, command):
            print(f"[{log_entry.log_type}] {log_entry.content}")
```

### Custom Command Processing

```python
processor = CommandProcessor()

# Natural language → Claude
user_input = "Explain this code"
if processor.should_wrap_with_claude(user_input):
    command = processor.process_command(user_input)
    # command = 'claude -p "Explain this code" --output-format text'
```

### Error Handling

```python
try:
    async for log_entry in service.execute_command_stream(sandbox_id, command):
        if log_entry.log_type == LogType.ERROR:
            handle_error(log_entry.content)
        else:
            process_output(log_entry.content)
except DaytonaStreamingError as e:
    logger.error(f"Streaming failed: {e}")
    # Implement fallback strategy
```

## Key Insights and Lessons Learned

### Claude CLI Streaming Investigation Results

Through extensive testing and analysis, we discovered that **Claude CLI is fundamentally incompatible with real-time streaming**:

#### Test Results Summary
| Command Type | Streaming Result | Evidence |
|-------------|------------------|----------|
| **Basic shell commands** (`echo`, `sleep`) | ✅ **Perfect streaming** | Immediate chunk reception |
| **All Claude CLI variants** | ❌ **Zero streaming output** | No chunks received despite successful execution |

#### Tested Claude CLI Variations
1. `claude -p "Say hello world"`
2. `claude -p "Say hello world" --output-format text`
3. `stdbuf -oL -eL claude -p "Say hello world" --output-format text`
4. `claude -p "Say hello world" --output-format text 2>&1`
5. `script -qec "claude -p \"Say hello world\" --output-format text" /dev/null`

**Result**: All variations resulted in timeout with 0 chunks received, confirming that Claude CLI buffers all output until completion.

### Root Cause Analysis

1. **Interactive Design**: Claude CLI is optimized for terminal interaction, not scripting
2. **Output Buffering**: All output is held until command completion
3. **TTY Dependency**: Requires terminal features not available in streaming contexts
4. **Long Processing Time**: Commands can take several minutes to complete

### Solution Architecture

The hybrid approach successfully addresses these limitations by:
- **Intelligent Detection**: Automatically identifying Claude commands
- **Progress Feedback**: Providing user feedback during long execution times
- **Result Streaming**: Chunking final responses for smooth presentation
- **Fallback Preservation**: Maintaining real streaming for compatible commands

---

## Revision History

| Version | Date | Changes |
|---------|------|---------|
| 1.0.0 | 2025-08-01 | Initial implementation with hybrid Claude streaming |

## References

- [Daytona SDK Documentation](https://www.daytona.io/docs/)
- [Claude CLI Reference](https://docs.anthropic.com/en/docs/claude-code/cli-reference)
- [FastAPI Streaming](https://fastapi.tiangolo.com/advanced/custom-response/#streamingresponse)
- [Daytona Log Streaming Guide](https://www.daytona.io/docs/log-streaming/)