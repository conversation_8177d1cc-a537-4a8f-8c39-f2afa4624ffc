#!/usr/bin/env python3
"""Simple validation script for streaming implementation"""

import sys
import json
import traceback
from datetime import datetime
from uuid import uuid4

# Test imports
def test_imports():
    """Test that all modules can be imported"""
    try:
        from models.streaming_models import TaskExecutionRequest, SSEMessage
        from utils.sse_formatter import SSEFormatter
        from routers.streaming import router
        
        return True
    except Exception as e:
        return False

def test_sse_formatter():
    """Test SSE formatter functionality"""
    try:
        from utils.sse_formatter import SSEFormatter
        
        formatter = SSEFormatter()
        
        # Test basic message formatting
        result = formatter.format_message("test content", "task_log", "task-123")
        assert result.startswith("data: ")
        assert result.endswith("\n\n")
        
        # Parse JSON content
        json_content = result[6:-2]  # Remove "data: " and "\n\n"
        message_data = json.loads(json_content)
        
        assert message_data["content"] == "test content"
        assert message_data["role"] == "assistant"
        assert message_data["metadata"]["type"] == "task_log"
        assert message_data["metadata"]["task_id"] == "task-123"
        
        # Test task start message
        start_msg = formatter.format_task_start("npm test", "task-456")
        start_data = json.loads(start_msg[6:-2])
        assert start_data["content"] == "Executing: npm test"
        assert start_data["metadata"]["type"] == "task_start"
        
        # Test task completion
        complete_msg = formatter.format_task_complete("task-789", 0, 5.25)
        complete_data = json.loads(complete_msg[6:-2])
        assert "Task completed (exit code: 0)" in complete_data["content"]
        assert complete_data["metadata"]["exit_code"] == 0
        
        # Test done message
        done_msg = formatter.format_done_message()
        assert done_msg == "data: [DONE]\n\n"
        
        return True
        
    except Exception as e:
        return False

def test_streaming_models():
    """Test Pydantic models"""
    try:
        from models.streaming_models import TaskExecutionRequest, SSEMessage
        
        # Test TaskExecutionRequest
        request = TaskExecutionRequest(
            command="echo hello",
            metadata={"source": "test"},
            working_directory="/tmp"
        )
        assert request.command == "echo hello"
        assert request.metadata == {"source": "test"}
        assert request.working_directory == "/tmp"
        
        # Test defaults
        request_default = TaskExecutionRequest(command="ls")
        assert request_default.working_directory == "/home/<USER>/workspace/repository"
        
        # Test SSEMessage
        message = SSEMessage(
            id="msg-1",
            content="test content",
            metadata={"type": "task_log"}
        )
        assert message.id == "msg-1"
        assert message.role == "assistant"
        assert message.content == "test content"
        
        # Test JSON serialization
        json_str = message.model_dump_json()
        parsed = json.loads(json_str)
        assert parsed["id"] == "msg-1"
        assert parsed["role"] == "assistant"
        
        return True
        
    except Exception as e:
        return False

def test_router_structure():
    """Test router structure and endpoints"""
    try:
        from routers.streaming import router
        from fastapi import FastAPI
        
        # Create test app and include router
        test_app = FastAPI()
        test_app.include_router(router)
        
        # Check that routes exist
        routes = [route.path for route in test_app.routes]
        expected_routes = [
            "/api/sandbox/{sandbox_id}/tasks/stream",
            "/api/sandbox/{sandbox_id}/tasks/current", 
            "/api/sandbox/{sandbox_id}/tasks/{task_id}"
        ]
        
        for expected_route in expected_routes:
            route_found = any(expected_route in route for route in routes)
            if not route_found:
                return False
        
        return True
        
    except Exception as e:
        return False

def test_database_integration():
    """Test database model integration"""
    try:
        from database import Task, TaskStatus
        import uuid
        
        # Test enum values
        assert TaskStatus.PENDING == "pending"
        assert TaskStatus.RUNNING == "running"
        assert TaskStatus.COMPLETED == "completed"
        assert TaskStatus.FAILED == "failed"
        assert TaskStatus.CANCELLED == "cancelled"
        
        return True
        
    except Exception as e:
        return False

def main():
    """Run all validation tests"""
    tests = [
        test_imports,
        test_sse_formatter,
        test_streaming_models,
        test_router_structure,
        test_database_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    if passed == total:
        return 0
    else:
        return 1

if __name__ == "__main__":
    sys.exit(main())